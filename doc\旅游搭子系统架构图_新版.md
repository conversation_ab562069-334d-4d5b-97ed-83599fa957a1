# 旅游搭子系统架构图 (新版)

## 系统概述

旅游搭子系统是基于LangGraph的智能旅行规划系统，采用端云协同架构，通过分层记忆体系、多智能体协作和实时状态管理，为用户提供个性化的旅行规划服务。

## 整体架构图

```
                                    旅游搭子系统架构图
                                   (端云协同 + 智能体架构)

┌─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┐
│                                          用户端                                                                    │
├─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                                                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────────────────┐  ┌─────────────────┐        │
│  │   车载数据源     │  │   主动感知       │  │            车载应用                  │  │   车端显示       │        │
│  │                 │  │                 │  │                                     │  │                 │        │
│  │  • 语音输入     │  │  • 对话意图提取  │  │  ┌─────────────────────────────────┐  │  │  • 形象对话     │        │
│  │  • 摄像头       │  │  • 情境主题     │  │  │         微信制器                 │  │  │  • 模块展示     │        │
│  │  • 车内传感器   │  │  • 分类CV训练题  │  │  │  • 车内语音 │ 情境主题 │ 指令时间 │  │  │                 │        │
│  │  • 车辆信号     │  │  • 环境信息感知  │  │  │  • 通风口   │ 导航专题 │ 导航专题 │  │  │  • 交互能力     │        │
│  │  • dms/oms     │  │  • 上报触发     │  │  │  • 导风口   │ 导航专题 │ 导航专题 │  │  │  • VUI显示      │        │
│  │                 │  │                 │  │  └─────────────────────────────────┘  │  │                 │        │
│  │                 │  │                 │  │                                     │  │  • HMI显示      │        │
│  │                 │  │                 │  │  ┌─────────────────────────────────┐  │  │  • 主屏适配     │        │
│  │                 │  │                 │  │  │         出行伴侣                 │  │  │                 │        │
│  │                 │  │                 │  │  │  • 偏好全局 │ 专属偏好 │ 人机对话 │  │  │                 │        │
│  │                 │  │                 │  │  │  • 导航专题 │ 导航专题 │ 导航专题 │  │  │                 │        │
│  │                 │  │                 │  │  │  • 导航专题 │ 导航专题 │ 导航专题 │  │  │                 │        │
│  │                 │  │                 │  │  └─────────────────────────────────┘  │  │                 │        │
│  │                 │  │                 │  │                                     │  │                 │        │
│  │                 │  │                 │  │  ┌─────────────────────────────────┐  │  │                 │        │
│  │                 │  │                 │  │  │         商务伴子                 │  │  │                 │        │
│  │                 │  │                 │  │  │  • AI导航 │ 清晰导航 │ 专题导航 │  │  │                 │        │
│  │                 │  │                 │  │  │  • 导航专题 │ 导航专题 │ AI VLOG │  │  │                 │        │
│  │                 │  │                 │  │  │  • 导航专题 │ 导航专题 │ 文化导游 │  │  │                 │        │
│  │                 │  │                 │  │  └─────────────────────────────────┘  │  │                 │        │
│  └─────────────────┘  └─────────────────┘  └─────────────────────────────────────┘  └─────────────────┘        │
│                                 │                           │                                  ▲                │
└─────────────────────────────────┼───────────────────────────┼──────────────────────────────────┼────────────────┘
                                 │                           │                                  │
                                 ▼                           ▼                                  │
┌─────────────────────────────────────────────────────────────────────────────────────────────┼────────────────┐
│                                          云端                                                  │                │
├─────────────────────────────────────────────────────────────────────────────────────────────┼────────────────┤
│                                                                                               │                │
│  ┌─────────────────┐  ┌─────────────────────────────────────────────────────────────────────┼────────────────┐
│  │   收动引擎       │  │                        中枢网关                                      │                │
│  │                 │  │                                                                      │                │
│  │  • VPA控制      │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────────────┼────────────────┐
│  │  • VUI开发      │  │  │   多媒体内容     │  │   上下文大A      │  │      情景智能体Agent     │                │
│  │  • AI-HMI控制  │  │  │   智能体Agent    │  │   (deepseek等模型) │  │    (deepseek等模型)     │                │
│  └─────────────────┘  │  │                 │  │                 │  │                        │                │
│                       │  │  • 智能作业     │  │  • 智能作业     │  │  • 智能作业            │                │
│  ┌─────────────────┐  │  │  • 智能作业     │  │  • 智能作业     │  │  • 智能作业            │                │
│  │   记忆系统       │  │  │  • 统一输出     │  │  • 统一输出     │  │  • 统一输出            │                │
│  │                 │  │  └─────────────────┘  └─────────────────┘  └─────────────────────────┼────────────────┘
│  │  • 记录导入     │  │                                                                      │
│  │  • 读取        │  │                                                                      │
│  └─────────────────┘  └──────────────────────────────────────────────────────────────────────┘
│                                                          │                                                     │
│                                                          ▼                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────────────────────────────────┐  │
│  │                                    多媒体生成                                                              │  │
│  │                                                                                                           │  │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │  │
│  │  │   基础组件       │  │   第三方信息检索  │  │   知识库        │  │   账号存储对接   │  │   数据分析       │  │  │
│  │  │                 │  │                 │  │                 │  │                 │  │                 │  │  │
│  │  │                 │  │                 │  │                 │  │                 │  │   原子能力Tools  │  │  │
│  └─────────────────────────────────────────────────────────────────────────────────────────────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────────────────────────────────────────┘
```

## 系统分层架构

### 用户端 (车载终端)

#### 1. 车载数据源
- **语音输入**: 用户语音指令和对话
- **摄像头**: 车内外视觉感知
- **车内传感器**: 环境感知和状态监测
- **车辆信号**: 车辆状态和行驶数据
- **DMS/OMS**: 驾驶员和乘员监测系统

#### 2. 主动感知
- **对话意图提取**: 自然语言理解和意图识别
- **情境主题**: 基于场景的主题识别
- **分类CV训练题**: 视觉识别和分类
- **环境信息感知**: 周围环境的智能感知
- **上报触发**: 事件驱动的数据上报

#### 3. 车载应用
- **微信制器**: 车载微信和社交功能
- **出行伴侣**: 个性化旅行规划和导航
- **商务伴子**: 商务出行专用功能模块

#### 4. 车端显示
- **形象对话**: 虚拟助手形象展示
- **模块展示**: 功能模块可视化
- **交互能力**: 多模态交互界面
- **VUI显示**: 语音用户界面
- **HMI显示**: 人机交互界面
- **主屏适配**: 车载主屏幕适配

### 云端 (智能服务)

#### 1. 收动引擎
- **VPA控制**: 虚拟个人助手控制中心
- **VUI开发**: 语音用户界面开发平台
- **AI-HMI控制**: AI驱动的人机交互控制

#### 2. 中枢网关
- **多媒体内容智能体Agent**: 内容生成和管理
- **上下文大A**: 基于DeepSeek等模型的上下文理解
- **情景智能体Agent**: 场景感知和智能决策

#### 3. 记忆系统
- **记录导入**: 用户数据和交互记录导入
- **读取**: 记忆数据检索和应用

#### 4. 多媒体生成
- **基础组件**: 核心功能组件库
- **第三方信息检索**: 外部数据源集成
- **知识库**: 领域知识和经验库
- **账号存储对接**: 用户账号和数据存储
- **数据分析**: 用户行为和偏好分析
- **原子能力Tools**: 基础工具和服务

## 核心特性

### 1. 端云协同架构
- **边缘计算**: 车端实时处理和响应
- **云端智能**: 复杂推理和大模型服务
- **数据同步**: 实时数据传输和同步

### 2. 多智能体协作
- **专业化分工**: 不同智能体负责特定领域
- **协同决策**: 多智能体联合决策机制
- **统一输出**: 一致的用户体验

### 3. 个性化服务
- **记忆驱动**: 基于用户记忆的个性化推荐
- **情境感知**: 基于场景的智能服务
- **持续学习**: 用户偏好的持续优化

### 4. 多模态交互
- **语音交互**: 自然语言对话
- **视觉交互**: 图像识别和显示
- **触控交互**: 触屏操作支持
- **手势交互**: 手势识别和控制

## 数据流转

### 上行数据流 (车端→云端)
1. **感知数据**: 传感器数据、语音、图像
2. **交互数据**: 用户指令、操作记录
3. **状态数据**: 车辆状态、位置信息
4. **偏好数据**: 用户选择、反馈信息

### 下行数据流 (云端→车端)
1. **决策结果**: AI推理和决策结果
2. **内容数据**: 多媒体内容和信息
3. **配置数据**: 系统配置和更新
4. **推荐数据**: 个性化推荐内容

## 技术实现架构

### LangGraph工作流引擎
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                            LangGraph状态图工作流                                    │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐                │
│  │   意图分析节点   │ -> │   偏好分析节点   │ -> │   策略分析节点   │                │
│  │                 │    │                 │    │                 │                │
│  │  • 核心意图提取  │    │  • 记忆检索     │    │  • 多城市策略   │                │
│  │  • 实体识别     │    │  • 偏好画像     │    │  • 驾驶情境     │                │
│  │  • 约束条件     │    │  • 个性化权重   │    │  • 路线优化     │                │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘                │
│           │                       │                       │                       │
│           └───────────────────────┼───────────────────────┘                       │
│                                   ▼                                               │
│  ┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐                │
│  │   行程生成节点   │ -> │   优化节点      │ -> │   结果输出节点   │                │
│  │                 │    │                 │    │                 │                │
│  │  • POI推荐      │    │  • 时间优化     │    │  • 格式化输出   │                │
│  │  • 路线规划     │    │  • 成本控制     │    │  • 记忆学习     │                │
│  │  • 住宿安排     │    │  • 体验优化     │    │  • 反馈收集     │                │
│  └─────────────────┘    └─────────────────┘    └─────────────────┘                │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### 分层记忆体系架构
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              分层记忆体系                                           │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                    L0: 即时记忆 (Redis)                                     │   │
│  │                   任务执行期间的实时状态                                     │   │
│  │                                                                             │   │
│  │  • task:{task_id} - 任务状态和进度                                          │   │
│  │  • user_context:{user_id} - 会话上下文                                     │   │
│  │  • temp_data:{session_id} - 临时计算数据                                   │   │
│  │  • TTL: 30分钟自动过期                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                    L1: 短期记忆 (Redis)                                     │   │
│  │                   会话期间的上下文缓存                                       │   │
│  │                                                                             │   │
│  │  • user_profile_cache:{user_id} - 用户画像缓存                             │   │
│  │  • recent_memories:{user_id} - 近期记忆片段                                │   │
│  │  • preference_cache:{user_id} - 偏好缓存                                   │   │
│  │  • TTL: 会话结束后清理                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                    L2: 长期记忆 (MySQL + MongoDB)                           │   │
│  │                    永久存储的用户记忆和交互历史                              │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    MySQL结构化存储                                  │    │   │
│  │  │                                                                     │    │   │
│  │  │  • user_memories - 用户记忆表                                       │    │   │
│  │  │  • user_summaries - 用户画像摘要                                   │    │   │
│  │  │  • ai_planning_sessions - 规划会话记录                             │    │   │
│  │  │  • travel_preferences - 旅行偏好配置                               │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                  MongoDB非结构化存储                                │    │   │
│  │  │                                                                     │    │   │
│  │  │  • ai_interaction_logs - 完整交互日志                               │    │   │
│  │  │  • user_memories - 动态记忆内容                                     │    │   │
│  │  │  • itinerary_history - 历史行程记录                                │    │   │
│  │  │  • learning_insights - 学习洞察数据                                │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                    L3: 知识图谱 (Neo4j)                                     │   │
│  │                   语义关系和推理知识库                                       │   │
│  │                                                                             │   │
│  │  • 用户-偏好关系图谱                                                        │   │
│  │  • 地点-特征知识网络                                                        │   │
│  │  • 行程-体验关联图谱                                                        │   │
│  │  • 智能推理和相似度计算                                                      │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### 外部服务集成
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              外部服务集成层                                         │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   高德地图MCP   │  │   大模型服务     │  │   第三方API     │  │   车载系统      │  │
│  │                 │  │                 │  │                 │  │                 │  │
│  │  • 地理编码     │  │  • DeepSeek     │  │  • 天气API      │  │  • CAN总线      │  │
│  │  • POI搜索      │  │  • OpenAI       │  │  • 票务API      │  │  • 传感器数据   │  │
│  │  • 路线规划     │  │  • 智谱AI       │  │  • 酒店API      │  │  • 车辆状态     │  │
│  │  • 实时交通     │  │  • 本地模型     │  │  • 美食API      │  │  • 位置信息     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 系统交互流程

### 典型用户交互流程
```
用户语音输入 -> 车端语音识别 -> 意图理解 -> 云端智能体处理 -> 记忆检索 ->
个性化推荐 -> 行程生成 -> 结果展示 -> 用户确认 -> 记忆学习 -> 持续优化
```

### 详细交互时序
```
车端                           云端                           数据库
 │                              │                              │
 │ 1. 语音输入                   │                              │
 ├─────────────────────────────>│                              │
 │                              │ 2. 意图分析                   │
 │                              ├─────────────────────────────>│
 │                              │                              │ 3. 记忆检索
 │                              │<─────────────────────────────┤
 │                              │ 4. 智能体协作                 │
 │ 5. 实时进度推送               │                              │
 │<─────────────────────────────┤                              │
 │                              │ 6. 行程生成                   │
 │                              ├─────────────────────────────>│
 │                              │                              │ 7. 结果存储
 │ 8. 最终结果展示               │<─────────────────────────────┤
 │<─────────────────────────────┤                              │
 │ 9. 用户反馈                   │                              │
 ├─────────────────────────────>│                              │
 │                              │ 10. 记忆学习                  │
 │                              ├─────────────────────────────>│
 │                              │                              │ 11. 偏好更新
```

## 部署架构

### 云端部署架构
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              云端服务集群                                           │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        负载均衡层                                            │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │   │
│  │  │   API网关       │  │   CDN加速       │  │   安全防护       │            │   │
│  │  │                 │  │                 │  │                 │            │   │
│  │  │  • 路由分发     │  │  • 静态资源     │  │  • WAF防护      │            │   │
│  │  │  • 限流控制     │  │  • 图片缓存     │  │  • DDoS防护     │            │   │
│  │  │  • 认证鉴权     │  │  • 边缘计算     │  │  • 数据加密     │            │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        应用服务层                                            │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │   │
│  │  │   旅游规划服务   │  │   记忆管理服务   │  │   智能体服务     │            │   │
│  │  │                 │  │                 │  │                 │            │   │
│  │  │  • LangGraph    │  │  • 记忆存储     │  │  • 多智能体     │            │   │
│  │  │  • 工作流引擎   │  │  • 记忆检索     │  │  • 协同决策     │            │   │
│  │  │  • 状态管理     │  │  • 学习优化     │  │  • 推理引擎     │            │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        数据存储层                                            │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │   │
│  │  │   Redis集群     │  │   MySQL集群     │  │   MongoDB集群   │            │   │
│  │  │                 │  │                 │  │                 │            │   │
│  │  │  • 缓存存储     │  │  • 结构化数据   │  │  • 文档存储     │            │   │
│  │  │  • 会话管理     │  │  • 用户画像     │  │  • 交互日志     │            │   │
│  │  │  • 实时状态     │  │  • 关系数据     │  │  • 非结构化     │            │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐                                                       │   │
│  │  │   Neo4j集群     │                                                       │   │
│  │  │                 │                                                       │   │
│  │  │  • 知识图谱     │                                                       │   │
│  │  │  • 语义推理     │                                                       │   │
│  │  │  • 关系查询     │                                                       │   │
│  │  └─────────────────┘                                                       │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

### 车端部署架构
```
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              车载终端架构                                           │
├─────────────────────────────────────────────────────────────────────────────────────┤
│                                                                                     │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        应用层                                                │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │   │
│  │  │   旅游搭子App   │  │   语音助手      │  │   导航应用       │            │   │
│  │  │                 │  │                 │  │                 │            │   │
│  │  │  • 行程展示     │  │  • 语音识别     │  │  • 路线导航     │            │   │
│  │  │  • 交互界面     │  │  • 语音合成     │  │  • 实时交通     │            │   │
│  │  │  • 个性化推荐   │  │  • 对话管理     │  │  • POI展示      │            │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        中间件层                                              │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │   │
│  │  │   通信中间件     │  │   数据同步       │  │   缓存管理       │            │   │
│  │  │                 │  │                 │  │                 │            │   │
│  │  │  • HTTP/WebSocket│  │  • 离线同步     │  │  • 本地缓存     │            │   │
│  │  │  • 消息队列     │  │  • 增量更新     │  │  • 数据压缩     │            │   │
│  │  │  • 协议适配     │  │  • 冲突解决     │  │  • 过期清理     │            │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        硬件抽象层                                            │   │
│  │                                                                             │   │
│  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐            │   │
│  │  │   传感器接口     │  │   显示接口       │  │   网络接口       │            │   │
│  │  │                 │  │                 │  │                 │            │   │
│  │  │  • 摄像头       │  │  • 主屏显示     │  │  • 4G/5G       │            │   │
│  │  │  • 麦克风       │  │  • 仪表盘       │  │  • WiFi        │            │   │
│  │  │  • 车辆传感器   │  │  • HUD抬头显示  │  │  • 蓝牙         │            │   │
│  │  └─────────────────┘  └─────────────────┘  └─────────────────┘            │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 技术优势

1. **实时响应**: 边缘计算确保毫秒级响应
2. **智能决策**: 大模型驱动的智能推理
3. **个性化**: 深度学习用户偏好和习惯
4. **可扩展**: 模块化架构支持功能扩展
5. **安全可靠**: 多层安全防护和容错机制
6. **记忆驱动**: 分层记忆体系实现持续学习
7. **多模态**: 支持语音、视觉、触控等多种交互方式
8. **端云协同**: 车端边缘计算与云端智能服务完美结合
