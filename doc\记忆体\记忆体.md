# AutoPilot AI 记忆体系统设计与实现 (V5.0)

**版本**: 5.0
**状态**: 架构设计稿
**目标读者**: 系统架构师、后端工程师、AI工程师

## 1. 概述 (Overview)

### 1.1. 系统目标
记忆系统的核心使命是赋能 AutoPilot AI，实现真正的个性化、自适应和持续进化的智能体验。通过学习和应用用户的偏好、习惯和交互历史，为用户提供个性化的AI助理体验。本系统是 AutoPilot AI 将一次性交互转化为可积累的用户资产、实现长期用户粘性的关键支柱。

### 1.2. 核心原则
- **简单高效**: 采用直接的数据库读写模式，确保系统性能与开发效率的平衡
- **异步记录**: 记忆写入采用异步模式，不影响用户交互的响应速度
- **智能过滤**: 通过重要性评估和去重机制，确保记忆库的质量
- **隐私优先**: 严格遵守数据安全边界，敏感信息进行适当脱敏处理
- **关系推理**: 通过知识图谱构建实体间的语义关系，支持深度关联查询

### 1.3. 记忆体系统架构

记忆体系统由三个核心存储层构成，每层承担不同的记忆职责：

#### MySQL - 结构化提炼数据库
- **用户画像**: 长期稳定的用户特征和偏好
- **核心记忆**: 经过筛选和验证的高价值记忆条目
- **特点**: 结构化存储，支持复杂查询和分析，存储精炼的关键信息

#### MongoDB - 非结构化全量库
- **交互日志**: 完整的用户交互历史记录
- **行为模式**: 用户的操作习惯和决策路径
- **原始数据**: 未经处理的完整交互数据
- **特点**: 灵活的文档存储，适合非结构化数据，保留完整上下文

#### Neo4j - 关系记忆库
- **关系网络**: 实体间的复杂关联关系
- **语义推理**: 基于图结构的知识推理
- **三元组知识**: 结构化的知识表示
- **特点**: 图数据库，擅长关系查询和路径分析

#### 外部知识源
- **实时信息**: 天气、交通、价格等动态数据
- **专业知识**: 通过API获取的领域专业信息
- **特点**: 实时性强，但需要网络访问

#### 任务状态管理 - Redis
- **临时状态**: 任务执行期间的中间状态和进度
- **会话缓存**: 当前任务相关的临时数据
- **特点**: 高性能读写，任务结束后清理，**不属于记忆体系统**

---

## 2. 系统架构 (System Architecture)

### 2.1. 整体架构图

```ascii
+------------------+
|  用户交互输入    |
+------------------+
        |
        v
+------------------+
|   MemoryManager  |
|   记忆读取与检索 |
+------------------+
        |
        v
+------------------+
|   LangGraph      |
|   智能决策引擎   |
+------------------+
        |
        v
+------------------+                    +------------------+
|   MemoryService  |                    |   多层记忆存储   |
|   记忆异步处理   |<------------------>| +-------------+  |
+------------------+                    | | L0/L1 缓存  |  |
                                        | | (Redis)     |  |
                                        | +-------------+  |
                                        | | L2 结构化   |  |
                                        | | (MySQL)     |  |
                                        | +-------------+  |
                                        | | L2 动态记忆 |  |
                                        | | (MongoDB)   |  |
                                        | +-------------+  |
                                        | | L3 知识图谱 |  |
                                        | | (Neo4j)     |  |
                                        | +-------------+  |
                                        | | L4 外部知识 |  |
                                        | | (API调用)   |  |
                                        | +-------------+  |
                                        +------------------+
```

### 2.2. 核心组件说明

#### 2.2.1. UserProfileDatabaseService (用户画像数据库服务)
- **职责**: 管理用户静态画像数据的查询和更新
- **数据源**: MySQL (dh_user_profile, dh_tripplanner数据库)
- **核心功能**: `get_user_summary()`, `get_travel_preferences()`, `get_historical_itineraries()`
- **文件位置**: `src/agents/services/user_profile_database_service.py`

#### 2.2.2. MemoryManager (动态记忆管理器)
- **职责**: 负责用户动态行为记忆的存储、读取和相关性计算
- **实现**: 通过MongoDB和Neo4j客户端进行记忆数据的CRUD操作
- **特点**: 支持基于内容、标签、上下文和关系的智能相关性匹配
- **核心功能**: `store_memory()`, `get_relevant_memories()`, `learn_from_interaction()`
- **文件位置**: `src/memory/memory_manager.py`

#### 2.2.3. MemoryService (记忆服务)
- **职责**: 提供记忆管理的原子化服务，包含缓存优化
- **实现**: 多数据库持久化 + Redis缓存的多层存储架构
- **特点**: 支持缓存策略，提供高性能的记忆查询和更新服务
- **核心功能**: `get_user_memories()`, `save_memory()`, `update_memory()`
- **文件位置**: `src/agents/services/memory_service.py`

#### 2.2.4. GraphMemoryService (知识图谱记忆服务)
- **职责**: 管理基于三元组的知识图谱记忆，支持关系推理
- **实现**: 通过Neo4j进行图数据的存储和查询
- **特点**: 支持深度关联查询、语义推理和知识融合
- **核心功能**: `extract_triplets()`, `store_graph_memory()`, `query_related_entities()`
- **文件位置**: `src/memory/graph_memory_service.py` (待实现)

#### 2.2.5. 多数据库存储架构

```ascii
+--------------------------------+
|          结构化数据层          |
|         (MySQL)                |
| - user_summaries (画像摘要)    |
| - user_travel_profiles (偏好)  |
| - itineraries (历史行程)       |
+--------------------------------+
                |
+--------------------------------+
|          动态记忆层            |
|         (MongoDB)              |
| - user_memories (交互记忆)     |
| - behavior_patterns (行为模式) |
| - learning_points (学习要点)   |
+--------------------------------+
                |
+--------------------------------+
|          知识图谱层            |
|         (Neo4j)                |
| - entities (实体节点)          |
| - relationships (关系边)       |
| - triplets (三元组知识)        |
+--------------------------------+
                |
+--------------------------------+
|          任务状态管理层        |
|         (Redis)                |
| - 任务执行状态 (临时)          |
| - 会话缓存 (临时)              |
| - 热点数据缓存                 |
+--------------------------------+
```

### 记忆体系统 (持久化存储)
- **MySQL层**: 存储结构化的用户画像和核心偏好
- **MongoDB层**: 存储动态的用户交互记忆和学习要点
- **Neo4j层**: 存储基于三元组的知识图谱，支持关系推理

### 任务状态管理 (临时存储)
- **Redis层**: 任务执行期间的状态管理和缓存加速，**不属于记忆体系统**

### 外部知识源
- **API层**: 实时获取外部知识（如地图、天气等）

---

## 3. 核心工作流

### 3.1. 记忆读取工作流 (任务启动时)

**目标**: 在任务（由`task_id`标识）启动的瞬间，为 `TravelPlannerAgent` 的初始状态(State)提供一个全面、多维度、已融合的上下文，以支撑后续所有分析和规划步骤（对应PRD中的阶段A和B）。

**核心原则**: 采用带依赖管理的“两阶段并行检索”模型，并通过“记忆融合智能体”进行结构化整合，确保流程兼顾高性能与高质量。

---
#### **步骤 1: 任务启动与参数准备**

当一个新规划任务启动时，系统首先准备好所有检索所需的基础参数。

*   **输入**:
    *   `task_id`: 本次任务的唯一ID。
    *   `user_id`: 发起任务的用户ID。
    *   `user_query`: 用户的原始自然语言请求 (例如：“帮我规划一个去泉州和厦门的5日自驾游”)。
*   **动作**:
    *   调用**数据分析师LLM**对 `user_query` 进行预处理，提取核心实体（如 `['泉州', '厦门']`）和关键词。
    *   准备多数据源查询参数，包括用户ID、实体列表、关键词等。

---
#### **步骤 2: 两阶段并行检索**

##### **阶段一: 无依赖并行查询 (The "Big Bang")**

系统在第一时间，以异步方式同时向所有无直接依赖的数据源发起查询。每个查询都带有严格的超时控制。

| 数据源 | 目标 | 查询参数 | 返回内容示例 | PRD支撑场景 |
| :--- | :--- | :--- | :--- | :--- |
| **MySQL** | 获取**结构化核心档案** | `user_id` | 包含用户画像、车辆信息、核心偏好（预算/住宿等级）的JSON对象。 | A.1, A.2, A.5 |
| **Neo4j** | 发现**关系与隐藏模式** | `user_id`, `核心实体` (如'泉州') | 关系列表，如 `(用户)-[:喜欢]->(历史遗迹)`；或推断出的洞察，如“海岛游爱好者”。 | A.0, A.3, A.4 |
| **MongoDB** | 查找**语义相关的历史记忆** | `user_id`, `关键词` | 与当前请求相关的历史记忆文档列表，包含完整的交互记录和行为模式。 | A.2 (历史续航), A.3 (具体兴趣点) |
| **Redis** | 获取**当前任务状态** | `task_id` | 当前任务的执行状态、临时变量等状态信息。 | 任务状态管理 |

##### **阶段二: 补充查询 (The "Follow-up")**

此阶段基于阶段一的查询结果，进行更深入的记忆检索和关联分析。

| 数据源 | 目标 | 查询参数 | 返回内容示例 | PRD支撑场景 |
| :--- | :--- | :--- | :--- | :--- |
| **MongoDB** | 获取**详细记忆内容** | 基于阶段一结果的`memory_id`列表 | 包含记忆原文、情感、上下文的完整JSON文档列表。 | A.2 (冬季续航的**具体描述**), A.3 (喜欢博物馆的**原因**) |
| **Neo4j** | 深度关系挖掘 | 基于阶段一发现的关系节点 | 更深层的关系路径和推理结果。 | A.3, A.4 (深度偏好分析) |

---
#### **步骤 3: 分层记忆处理 (Layered Memory Processing)**

采用**"快速摘要 + 按需深入"**的分层策略，确保响应速度的同时保证记忆质量。

##### **第一层: 快速摘要聚合**

直接使用数据库中的预处理摘要，无需调用LLM。

1.  **MySQL摘要读取**:
    *   **动作**: 直接读取用户画像中的核心偏好摘要（预算、住宿等级、活动偏好等）
    *   **数据格式**: 结构化JSON，已预处理，可直接使用
2.  **MongoDB关键记忆**:
    *   **动作**: 基于关键词匹配，快速检索Top-5相关记忆的摘要字段
    *   **数据格式**: 每条记忆的`summary`字段，包含核心要点
3.  **Neo4j关系摘要**:
    *   **动作**: 查询预计算的用户标签和关系摘要
    *   **数据格式**: 用户标签列表和关键关系描述
4.  **规则化合并**:
    *   **动作**: 使用预定义规则进行简单合并，遵循信任链：`MongoDB最新交互 > MySQL核心偏好 > Neo4j推断`
    *   **冲突处理**: 基于时间戳和优先级的简单规则，无需LLM

##### **第二层: 智能体深度融合 (按需触发)**

仅在检测到以下情况时，才异步调用**记忆融合智能体**进行深度处理：

**触发条件**:
1. **复杂冲突检测**: 发现不同数据源间存在重大矛盾（如预算差异>50%）
2. **语义歧义**: 关键词匹配结果置信度过低（<0.6）
3. **用户明确要求**: 用户询问"为什么推荐这个"等需要深度解释的问题
4. **新用户冷启动**: 历史记忆不足，需要推理补充
5. **特殊场景**: 涉及安全、健康等敏感决策

**深度处理流程**:
1. **智能体调用**: 异步启动MemoryFusionAgent，进行语义理解和冲突解决
2. **结果缓存**: 将融合结果缓存到Redis，供后续快速访问
3. **增量更新**: 更新用户的摘要数据，优化下次查询
4. **用户通知**: 通过SSE推送更精确的记忆洞察

---
#### **步骤 4: 生成分层上下文并注入状态**

根据分层处理策略，系统会生成不同详细程度的上下文，并通过Redis的任务状态管理功能存储。

##### **快速上下文 (第一层输出)**

**生成时机**: 每次查询都会立即生成，响应时间<100ms
**数据来源**: 预处理摘要 + 规则化合并
**适用场景**: 90%的常规对话场景

```json
{
  "user_id": "user_123",
  "task_id": "task_67890",
  "context_type": "fast_summary",
  "context": {
    "user_profile": {
      "name": "张三",
      "driving_preference": "稳健型",
      "vehicle_info": {
        "model": "Tesla Model Y",
        "nominal_range_km": 545
      }
    },
    "core_preferences": {
      "accommodation_budget": "1000-1500元",
      "activity_tags": ["历史遗迹", "自然风光"],
      "food_exclusions": ["海鲜"]
    },
    "key_memories": [
      {
        "summary": "冬季续航75%，不喜欢行程太满",
        "source": "MongoDB",
        "last_updated": "2024-01-15"
      }
    ],
    "user_tags": ["历史文化爱好者", "稳健驾驶", "休闲节奏"]
  },
  "metadata": {
    "generation_time": "2024-01-20T09:15:00Z",
    "processing_duration_ms": 85,
    "fusion_agent_called": false
  }
}
```

##### **深度上下文 (第二层输出)**

**生成时机**: 仅在触发深度融合条件时异步生成
**数据来源**: MemoryFusionAgent智能处理结果
**适用场景**: 复杂决策、冲突解决、深度解释

```json
{
  "user_id": "user_123",
  "task_id": "task_67890",
  "context_type": "deep_fusion",
  "context": {
    "user_profile": {
      "name": "张三",
      "driving_preference": "稳健型",
      "vehicle_info": {
        "model": "Tesla Model Y",
        "nominal_range_km": 545,
        "winter_efficiency": 0.75
      }
    },
    "core_preferences": {
      "accommodation_budget": "1000-1500元",
      "activity_tags": ["历史遗迹", "自然风光"],
      "food_exclusions": ["海鲜"],
      "pace_preference": "轻松节奏，避免过度安排"
    },
    "relevant_memories": [
      {
        "content": "用户反馈其车辆在冬季的实际续航约为标称的75%，建议充电规划更保守。",
        "source": "MongoDB",
        "relevance_score": 0.95,
        "semantic_analysis": "对续航焦虑敏感，需要充电保障"
      },
      {
        "content": "用户明确表示不喜欢行程安排得太满，希望可以睡个懒觉，享受慢节奏旅行。",
        "source": "MongoDB",
        "relevance_score": 0.92,
        "semantic_analysis": "偏好休闲旅行风格，重视舒适度"
      }
    ],
    "inferred_insights": [
      {
        "insight": "该用户是一个'历史文化爱好者'，同时注重旅行的舒适性和可控性。",
        "source": "MemoryFusionAgent",
        "confidence": 0.89,
        "reasoning": "基于多次历史遗迹选择和休闲节奏偏好的综合分析"
      }
    ]
  },
  "metadata": {
    "generation_time": "2024-01-20T09:15:30Z",
    "processing_duration_ms": 1250,
    "fusion_agent_called": true,
    "trigger_reason": "复杂偏好分析需求",
    "total_memories_analyzed": 15
  }
}
```

这些分层上下文将通过Redis的任务状态管理功能存储，作为`TravelPlannerAgent`启动时可直接使用的"第一份情报"，为PRD中定义的**阶段A: 交互式分析**的每一个步骤提供决策依据。

### 3.2. 记忆写入工作流 (Memory Storage Flow)

```ascii
+--------------------------------+
|      LangGraph - 最终节点      |
| (调用 MemoryManager)           |
+--------------------------------+
                |
                v
+--------------------------------+
|       MemoryManager            |
| 1. 提取学习要点 (LLM)          |
| 2. 提取三元组知识 (LLM)        |
| 3. 异步并行写入多个存储层      |
+--------------------------------+
                |
                v
+--------------------------------+
|         并行存储处理           |
| MongoDB: 动态记忆              |
| Neo4j: 知识图谱                |
| MySQL: 用户画像更新            |
+--------------------------------+
```

---

## 4. GRAG 知识图谱记忆系统

### 4.1. 系统概述
GRAG (Graph-based Retrieval Augmented Generation) 知识图谱记忆系统是对现有记忆体系统的重要补充，通过构建基于三元组的知识图谱，实现实体间的语义关系建模和深度关联查询。

### 4.2. 技术架构

```ascii
+--------------------------------+
|        用户交互内容            |
+--------------------------------+
                |
                v
+--------------------------------+
|      三元组提取 (LLM)          |
| - 实体识别                     |
| - 关系抽取                     |
| - 属性提取                     |
+--------------------------------+
                |
                v
+--------------------------------+
|      知识图谱存储 (Neo4j)      |
| - 实体节点 (Entity)            |
| - 关系边 (Relationship)        |
| - 属性信息 (Properties)        |
+--------------------------------+
                |
                v
+--------------------------------+
|      混合检索引擎              |
| - 图遍历查询                   |
| - 语义关键词匹配               |
| - 结果融合排序                 |
+--------------------------------+
```

### 4.3. 核心特性
- **关系推理**: 支持多跳关系查询，发现隐含的实体关联
- **语义理解**: 基于图结构的语义表示，提升知识理解能力
- **知识融合**: 自动去重和合并相似实体，保持知识一致性
- **异步处理**: 采用"发后即忘"策略，避免阻塞主线程

### 4.4. 实施策略
- **渐进式集成**: 作为现有架构的补充，不替换现有数据库
- **混合检索**: 结合传统关键词检索和图遍历查询
- **性能优化**: 通过索引优化和查询缓存提升检索效率
- **可配置性**: 支持开启/关闭GRAG功能，灵活适应不同场景

---

## 5. 记忆数据记录流程 (Memory Data Recording Flow)

### 5.1. 三阶段记录模式

**阶段一：任务初始化与记忆预备**
- 生成唯一task_id，在MySQL、MongoDB、Redis中创建记录槽位
- 并行初始化各存储层的基础数据结构

**阶段二：记忆读取与任务执行**
- 并行从多个存储层检索相关记忆（MySQL静态画像、MongoDB动态记忆、Neo4j知识图谱）
- 实时记录任务执行状态到Redis，支持SSE流式推送
- 动态更新执行过程中的中间状态和结果

**阶段三：任务完成与记忆沉淀**
- 从Redis读取完整执行记录，更新MongoDB和MySQL的最终状态
- 清理Redis临时数据，释放资源
- 触发记忆智能体进行学习要点提取和知识图谱更新

### 5.2. 记忆智能体协同架构

**核心记忆智能体集群：**
- **记忆提取智能体 (MemoryExtractionAgent)**：分析交互日志，识别学习要点和用户偏好模式
- **记忆评估智能体 (MemoryEvaluationAgent)**：评估记忆价值，执行去重合并和质量控制
- **记忆更新智能体 (MemoryUpdateAgent)**：更新用户画像，插入新记忆，维护数据一致性
- **知识图谱智能体 (GraphMemoryAgent)**：提取三元组知识，构建实体关系，执行语义推理

**支撑服务层：**
- **记忆检索服务**：直接数据库查询模式，毫秒级响应，支持高并发
- **数据存储服务**：MySQL + MongoDB + Neo4j，保证ACID事务和数据持久化
- **缓存加速服务**：Redis多层缓存，智能预加载，亚毫秒级访问

### 5.3. 核心工作流程

#### 5.3.1. 记忆检索服务 (Memory Retrieval Service)

**设计理念**: 采用直接数据库查询模式，确保毫秒级响应和高可靠性。

**核心职责**:
- 多源数据检索：并行查询MySQL、MongoDB、Neo4j
- 相关性计算：关键词匹配 + 时间衰减 + 重要性加权
- 智能缓存：Redis热点数据缓存，支持智能预加载
- 结构化输出：标准化JSON格式，包含元数据和性能统计

**工作流程**: 查询预处理 → 缓存检查 → 多源检索 → 相关性计算 → 结果排序 → 缓存更新 → 结构化输出

#### 5.3.2. 记忆提取智能体 (MemoryExtractionAgent)
**核心职责**: 分析任务执行日志，使用LLM提取学习要点，识别用户偏好变化和行为模式
**触发时机**: 任务完成后的记忆沉淀阶段

#### 5.3.3. 记忆评估智能体 (MemoryEvaluationAgent)
**核心职责**: 评估记忆价值，计算重要性得分，执行去重合并和质量控制
**评估维度**: 新颖性、一致性、可操作性、时效性

#### 5.3.4. 记忆更新智能体 (MemoryUpdateAgent)
**核心职责**: 将高价值记忆持久化到MongoDB，更新MySQL用户画像，维护记忆关联关系

### 5.4. 异常处理与性能优化

#### 5.4.1. 容错机制
- **任务执行超时**: 主动监控，标记失败，保存部分记忆
- **数据库连接失败**: 连接重试，切换备用连接，故障转移
- **记忆提取失败**: 降级处理，跳过记忆学习，异步重试
- **存储空间不足**: 清理策略，归档旧记忆，空间监控告警

#### 5.4.2. 性能优化
- **分层缓存**: L0任务状态缓存，L1热点记忆缓存，L2用户画像缓存
- **异步处理**: 消息队列异步写入，批量数据库操作，延迟加载
- **智能预加载**: 基于用户行为预测、地理位置触发、时间模式识别

---

## 6. 数据结构设计 (Data Structure Design)

### 6.1. MySQL 数据表结构

#### 6.1.1. 用户画像表 (ai_user_profiles)
**核心字段**: user_id, user_summary, travel_preferences, demographic_info, interaction_stats
**关键索引**: user_id(UNIQUE), last_updated, status

#### 6.1.2. 任务规划会话表 (ai_planning_sessions)
**核心字段**: task_id, user_id, session_type, execution_status, start_time, end_time
**关键索引**: task_id(UNIQUE), user_id, execution_status, time_range

### 6.2. MongoDB 集合结构

#### 6.2.1. 交互日志集合 (ai_interaction_logs)
**核心字段**: interaction_id, task_id, user_id, user_query, agent_response, extracted_preferences, importance_score
**关键索引**: user_id+created_at, task_id+interaction_type, importance_score

#### 6.2.2. 记忆条目集合 (ai_memory_entries)
**核心字段**: memory_id, user_id, memory_type, content, structured_data, importance_score, related_memories
**关键索引**: user_id+importance_score, memory_type+created_at, content(text)

### 6.3. Neo4j 图数据结构

#### 6.3.1. 节点类型 (Node Types)
- **User**: 用户实体，属性包含user_id, name, preferences
- **Entity**: 通用实体，如地点、活动、偏好等
- **Concept**: 抽象概念，如旅行风格、预算等级
- **Memory**: 记忆节点，连接用户与相关实体

#### 6.3.2. 关系类型 (Relationship Types)
- **PREFERS**: 用户偏好关系，权重表示偏好强度
- **VISITED**: 用户访问过的地点
- **RELATES_TO**: 实体间的关联关系
- **CONTAINS**: 包含关系，如地区包含城市
- **SIMILAR_TO**: 相似性关系，用于推荐

#### 6.3.3. 三元组示例
```cypher
(User:user_123)-[PREFERS:0.9]->(Entity:cultural_tourism)
(Entity:Paris)-[CONTAINS]->(Entity:Louvre_Museum)
(Memory:mem_456)-[RELATES_TO]->(Entity:historical_sites)
```

### 6.4. Redis 缓存结构

#### 6.4.1. 任务执行状态 (L0)
**结构**: Hash `task:{task_id}`
**字段**: status, user_id, progress, intermediate_results
**TTL**: 30分钟

#### 6.4.2. 会话记忆 (L1)
**结构**: Hash `session:{session_id}`
**字段**: current_intent, temp_preferences, interaction_count
**TTL**: 2小时

#### 6.4.3. 热点记忆缓存
**结构**: List `user:{user_id}:hot_memories`
**内容**: 高频访问的记忆条目JSON
**TTL**: 4小时

### 6.5. 数据一致性策略

**写入流程**: Redis实时状态 → MongoDB持久化 → MySQL聚合更新 → Neo4j关系构建
**读取流程**: Redis缓存检查 → 多源并行查询 → 结果整合排序
**同步机制**: 实时同步(Redis→MongoDB)，批量同步(MongoDB→MySQL)，异步构建(Neo4j)

