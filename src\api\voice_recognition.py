from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
import asyncio
import base64
import json
import logging
from tools.Tele.sign_utils import getAuth
import websockets
from src.core.config import get_settings

router = APIRouter(prefix="/voice", tags=["voice_recognition"])

# 获取ws_url从配置
REMOTE_ASR_WS_URL = getattr(get_settings().tele_sign, "voice").ws_url

logger = logging.getLogger("voice_recognition")

@router.websocket("/ws/{session_id}")
async def websocket_voice_recognition(websocket: WebSocket, session_id: str):
    await websocket.accept()
    asr_ws = None
    asr_send_task = None
    asr_recv_task = None
    asr_send_queue = None
    try:
        while True:
            data = await websocket.receive_text()
            msg = json.loads(data)
            rec_status = msg.get("rec_status")
            # 0: start, 1: audio chunk, 2: end
            if rec_status == 0:
                # 新的识别会话，连接远程ASR
                if asr_ws:
                    await asr_ws.close()
                # 获取远程ASR鉴权头
                headers = getAuth.get_auth()
                asr_ws = await websockets.connect(REMOTE_ASR_WS_URL, extra_headers=headers)
                # 发送初始请求到远程ASR
                start_json = {
                    'option': msg.get('option', {'sample_rate': 16000}),
                    'req_id': msg.get('req_id', session_id),
                    'rec_status': 0
                }
                await asr_ws.send(json.dumps(start_json))
                # 启动后台任务转发ASR结果到前端
                asr_send_queue = asyncio.Queue()
                asr_recv_task = asyncio.create_task(relay_asr_to_client(asr_ws, websocket))
                asr_send_task = asyncio.create_task(forward_audio_to_asr(asr_ws, asr_send_queue))
            elif rec_status == 1:
                # 音频流分片，放入队列
                if asr_send_queue:
                    await asr_send_queue.put(msg.get("audio_stream"))
            elif rec_status == 2:
                # 结束标志，通知ASR
                if asr_send_queue:
                    await asr_send_queue.put(None)  # None表示结束
                if asr_send_task:
                    await asr_send_task
                if asr_recv_task:
                    await asr_recv_task
                if asr_ws:
                    await asr_ws.close()
                asr_ws = None
                asr_send_task = None
                asr_recv_task = None
                asr_send_queue = None
            else:
                await websocket.send_text(json.dumps({"code": 400, "message": "未知的rec_status"}))
    except WebSocketDisconnect:
        logger.info(f"WebSocket disconnected: {session_id}")
        if asr_ws:
            await asr_ws.close()
    except Exception as e:
        logger.error(f"WebSocket error: {e}")
        await websocket.close()
        if asr_ws:
            await asr_ws.close()

async def forward_audio_to_asr(asr_ws, queue):
    """
    从队列读取音频分片并发送到远程ASR
    """
    try:
        while True:
            chunk = await queue.get()
            if chunk is None:
                # 发送结束标志
                await asr_ws.send(json.dumps({"rec_status": 2}))
                break
            # 发送音频分片
            mid_json = {
                "audio_stream": chunk,
                "rec_status": 1
            }
            await asr_ws.send(json.dumps(mid_json))
            await asyncio.sleep(0.02)  # 控制发送速率，防止过快
    except Exception as e:
        logger.error(f"forward_audio_to_asr error: {e}")

async def relay_asr_to_client(asr_ws, websocket):
    """
    从远程ASR接收识别结果并转发给前端
    """
    try:
        total_text = ""
        while True:
            message = await asr_ws.recv()
            print('message', message)
            msg = json.loads(message)
            if msg['code'] in [10007]:
                #10007 Non-real-time audio data
                continue
            await websocket.send_text(message)
            if msg['res_status'] in [3, 4]:
                total_text += msg['data']['results'][0]['text']
            if msg['res_status'] == 4:
                print(f"total_text: {total_text}")
            if msg.get('res_status') == 4:
                break
    except Exception as e:
        logger.error(f"relay_asr_to_client error: {e}")
