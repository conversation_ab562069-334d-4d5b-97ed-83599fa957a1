# 旅游搭子系统架构图

## 系统概述

旅游搭子系统是基于LangGraph的智能旅行规划系统，采用状态图工作流、分层记忆体系和实时状态管理的设计理念。系统通过完整的记忆体架构实现用户偏好学习、个性化推荐和持续优化，支持全自动规划模式和交互式规划模式。

## 核心架构图

```
                                    旅游搭子系统架构图
                                   (基于LangGraph实现)

┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                   用户交互层                                          │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐          │
│  │   车载终端   │    │   移动APP   │    │   Web界面   │    │   语音助手   │          │
│  │  Car Display │    │ Mobile App  │    │ Web Portal │    │Voice Assistant│          │
│  └─────────────┘    └─────────────┘    └─────────────┘    └─────────────┘          │
│           │                 │                 │                 │                   │
│           └─────────────────┼─────────────────┼─────────────────┘                   │
│                             │                 │                                     │
└─────────────────────────────┼─────────────────┼─────────────────────────────────────┘
                              │                 │
┌─────────────────────────────┼─────────────────┼─────────────────────────────────────┐
│                           API网关/中枢系统                                           │
├─────────────────────────────┼─────────────────┼─────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                        输入适配器 (Input Adapter)                           │   │
│  │                    语音转文本 | 格式化 | 意图预处理                           │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      任务管理器 (Task Manager)                               │   │
│  │                  生成task_id | 三方占坑 | 状态跟踪                           │   │
│  │                                                                             │   │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐                          │   │
│  │  │   MySQL     │  │  MongoDB    │  │   Redis     │                          │   │
│  │  │ 任务状态表   │  │ 交互日志集合 │  │ 实时作战室   │                          │   │
│  │  │ (占坑)      │  │ (骨架文档)   │  │ (Hash存储)  │                          │   │
│  │  └─────────────┘  └─────────────┘  └─────────────┘                          │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      SSE流式输出 (SSE Streaming)                             │   │
│  │                   实时进度推送 | 事件通知 | 结果分发                          │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                            LangGraph旅行规划引擎                                     │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                   TravelPlannerAgentLangGraph                               │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    运行模式选择器                                    │    │   │
│  │  │                                                                     │    │   │
│  │  │  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐      │    │   │
│  │  │  │   全自动模式     │  │   分析模式       │  │   交互模式       │      │    │   │
│  │  │  │ plan_travel_    │  │ stream_run_     │  │ plan_travel_    │      │    │   │
│  │  │  │ automatic()     │  │ analysis_only() │  │ interactive()   │      │    │   │
│  │  │  └─────────────────┘  └─────────────────┘  └─────────────────┘      │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                        │                                     │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                  TravelPlannerGraph                                 │    │   │
│  │  │                   (LangGraph状态图)                                 │    │   │
│  │  │                                                                     │    │   │
│  │  │  ┌─────────────────────────────────────────────────────────────┐    │    │   │
│  │  │  │                    路由节点                                  │    │    │   │
│  │  │  │                   (Router)                                  │    │    │   │
│  │  │  │                                                             │    │    │   │
│  │  │  │  • 判断是否跳过分析阶段                                      │    │    │   │
│  │  │  │  • 基于analysis_completed标记路由                           │    │    │   │
│  │  │  │  • 支持分析阶段和规划阶段的独立执行                          │    │    │   │
│  │  │  └─────────────────────────────────────────────────────────────┘    │    │   │
│  │  │                                        │                             │    │   │
│  │  │                        ┌───────────────┼───────────────┐             │    │   │
│  │  │                        │               │               │             │    │   │
│  │  │                   分析阶段         规划阶段        错误处理            │    │   │
│  │  │                        │               │               │             │    │   │
│  │  └─────────────────────────┼───────────────┼───────────────┼─────────────┘    │   │
│  └─────────────────────────────┼───────────────┼───────────────┼─────────────────┘   │
└─────────────────────────────────┼───────────────┼───────────────┼─────────────────────┘
                                 │               │               │
┌─────────────────────────────────┼───────────────┼───────────────┼─────────────────────┐
│                              分析阶段工作流                     │                     │
├─────────────────────────────────┼───────────────┼───────────────┼─────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐│                     │
│  │                    核心意图分析节点                          ││                     │
│  │              (core_intent_analyzer_node)                   ││                     │
│  │                                                             ││                     │
│  │  • 解析用户查询意图 (目的地、天数、主题等)                   ││                     │
│  │  • 提取旅行偏好和约束条件                                   ││                     │
│  │  • 确定规划模式 (精准续航 vs 通用辅助)                      ││                     │
│  │  • 生成CoreIntent结构化数据                                 ││                     │
│  └─────────────────────────────────────────────────────────────┘│                     │
│                                 │                                │                     │
│  ┌─────────────────────────────────────────────────────────────┐│                     │
│  │                   多城市策略分析节点                         ││                     │
│  │              (multi_city_strategy_node)                    ││                     │
│  │                                                             ││                     │
│  │  • 分析是否为多城市旅行                                     ││                     │
│  │  • 制定城市间交通策略                                       ││                     │
│  │  • 优化城市游览顺序                                         ││                     │
│  │  • 分配各城市时间                                           ││                     │
│  └─────────────────────────────────────────────────────────────┘│                     │
│                                 │                                │                     │
│  ┌─────────────────────────────────────────────────────────────┐│                     │
│  │                   驾驶情境分析节点                           ││                     │
│  │            (driving_context_analyzer_node)                 ││                     │
│  │                                                             ││                     │
│  │  • 分析车辆类型和续航能力                                   ││                     │
│  │  • 制定充电/加油策略                                        ││                     │
│  │  • 优化驾驶路线                                             ││                     │
│  │  • 评估驾驶约束条件                                         ││                     │
│  └─────────────────────────────────────────────────────────────┘│                     │
│                                 │                                │                     │
│  ┌─────────────────────────────────────────────────────────────┐│                     │
│  │                    偏好分析节点                              ││                     │
│  │               (preference_analyzer_node)                   ││                     │
│  │                                                             ││                     │
│  │  • 分析用户历史偏好                                         ││                     │
│  │  • 结合当前查询生成偏好画像                                 ││                     │
│  │  • 确定景点、美食、住宿偏好                                 ││                     │
│  │  • 生成PreferenceProfile结构                               ││                     │
│  └─────────────────────────────────────────────────────────────┘│                     │
└─────────────────────────────────────────────────────────────────┼─────────────────────┘
                                                                 │
┌─────────────────────────────────────────────────────────────────┼─────────────────────┐
│                              规划阶段工作流                     │                     │
├─────────────────────────────────────────────────────────────────┼─────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐│                     │
│  │                    行程生成节点                              ││                     │
│  │              (itinerary_generator_node)                    ││                     │
│  │                                                             ││                     │
│  │  • 基于分析结果生成详细行程                                 ││                     │
│  │  • 调用AnalysisService.generate_itinerary()                ││                     │
│  │  • 生成每日行程安排 (DailyItinerary)                       ││                     │
│  │  • 包含景点、餐厅、住宿、交通安排                           ││                     │
│  └─────────────────────────────────────────────────────────────┘│                     │
│                                 │                                │                     │
│  ┌─────────────────────────────────────────────────────────────┐│                     │
│  │                     优化节点                                 ││                     │
│  │                 (optimizer_node)                           ││                     │
│  │                                                             ││                     │
│  │  • 优化行程时间安排                                         ││                     │
│  │  • 优化路线和交通方式                                       ││                     │
│  │  • 成本优化和预算控制                                       ││                     │
│  │  • 标记任务完成状态                                         ││                     │
│  └─────────────────────────────────────────────────────────────┘│                     │
└─────────────────────────────────────────────────────────────────┼─────────────────────┘
                                                                 │
┌─────────────────────────────────────────────────────────────────┼─────────────────────┐
│                              错误处理工作流                     │                     │
├─────────────────────────────────────────────────────────────────┼─────────────────────┤
│  ┌─────────────────────────────────────────────────────────────┐│                     │
│  │                    错误处理节点                              ││                     │
│  │                (error_handler_node)                        ││                     │
│  │                                                             ││                     │
│  │  • 捕获和处理各阶段错误                                     ││                     │
│  │  • 记录错误信息和堆栈                                       ││                     │
│  │  • 设置错误状态和消息                                       ││                     │
│  │  • 触发错误事件通知                                         ││                     │
│  └─────────────────────────────────────────────────────────────┘│                     │
└─────────────────────────────────────────────────────────────────┼─────────────────────┘
                                                                 │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                状态管理层                                            │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      TravelPlanState (状态定义)                             │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                      核心状态字段                                   │    │   │
│  │  │                                                                     │    │   │
│  │  │  • task_id: str                    # 任务唯一标识                   │    │   │
│  │  │  • user_id: str                    # 用户标识                       │    │   │
│  │  │  • query: str                      # 用户原始查询                   │    │   │
│  │  │  • planning_mode: PlanningMode     # 规划模式                       │    │   │
│  │  │  • current_stage: ProcessingStage  # 当前处理阶段                   │    │   │
│  │  │  • analysis_completed: bool        # 分析阶段完成标记               │    │   │
│  │  │  • events: List[Dict]              # 事件日志                       │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                      分析结果状态                                   │    │   │
│  │  │                                                                     │    │   │
│  │  │  • user_profile: UserProfile       # 用户画像                       │    │   │
│  │  │  • vehicle_info: VehicleInfo       # 车辆信息                       │    │   │
│  │  │  • core_intent: CoreIntent         # 核心意图                       │    │   │
│  │  │  • multi_city_strategy: MultiCity  # 多城市策略                     │    │   │
│  │  │  • driving_context: DrivingContext # 驾驶情境                       │    │   │
│  │  │  • preference_profile: Preference  # 偏好画像                       │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                      规划结果状态                                   │    │   │
│  │  │                                                                     │    │   │
│  │  │  • itinerary: TravelItinerary      # 完整行程                       │    │   │
│  │  │  • optimization_result: Dict       # 优化结果                       │    │   │
│  │  │  • error_message: Optional[str]    # 错误信息                       │    │   │
│  │  │  • completed: bool                 # 完成标记                       │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      状态持久化管理                                         │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    MemorySaver                                      │    │   │
│  │  │                 (LangGraph检查点)                                   │    │   │
│  │  │                                                                     │    │   │
│  │  │  • 自动保存工作流状态                                               │    │   │
│  │  │  • 支持状态恢复和回滚                                               │    │   │
│  │  │  • 与Redis实时状态同步                                              │    │   │
│  │  │  • 提供状态版本管理                                                 │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    状态辅助函数                                      │    │   │
│  │  │                                                                     │    │   │
│  │  │  • create_initial_state()          # 创建初始状态                   │    │   │
│  │  │  • add_event_to_state()            # 添加事件到状态                 │    │   │
│  │  │  • update_stage()                  # 更新处理阶段                   │    │   │
│  │  │  • mark_analysis_complete()        # 标记分析完成                   │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                                工具集成层                                            │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      高德地图MCP工具集                                       │   │
│  │                     (Amap MCP Tools)                                       │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                      地理服务工具                                   │    │   │
│  │  │                                                                     │    │   │
│  │  │  • maps_geo                        # 地址解析和地理编码             │    │   │
│  │  │  • maps_regeo                      # 逆地理编码                     │    │   │
│  │  │  • maps_text_search                # POI文本搜索                    │    │   │
│  │  │  • maps_around_search              # 周边搜索                       │    │   │
│  │  │  • maps_search_detail              # POI详情查询                    │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                      路线规划工具                                   │    │   │
│  │  │                                                                     │    │   │
│  │  │  • maps_direction_driving          # 驾车路线规划                   │    │   │
│  │  │  • maps_direction_walking          # 步行路线规划                   │    │   │
│  │  │  • maps_direction_transit          # 公交路线规划                   │    │   │
│  │  │  • maps_direction_bicycling        # 骑行路线规划                   │    │   │
│  │  │  • maps_distance_matrix            # 距离矩阵计算                   │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                      信息查询工具                                   │    │   │
│  │  │                                                                     │    │   │
│  │  │  • maps_weather                    # 天气查询                       │    │   │
│  │  │  • maps_static_map                 # 静态地图生成                   │    │   │
│  │  │  • maps_ip_location                # IP定位                         │    │   │
│  │  │  • maps_administrative_region      # 行政区域查询                   │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      分析服务工具集                                         │   │
│  │                   (Analysis Services)                                      │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    AnalysisService                                  │    │   │
│  │  │                                                                     │    │   │
│  │  │  • analyze_core_intent()           # 核心意图分析                   │    │   │
│  │  │  • analyze_multi_city_strategy()   # 多城市策略分析                 │    │   │
│  │  │  • analyze_driving_context()       # 驾驶情境分析                   │    │   │
│  │  │  • analyze_preference_profile()    # 偏好画像分析                   │    │   │
│  │  │  • generate_itinerary()            # 行程生成                       │    │   │
│  │  │  • optimize_itinerary()            # 行程优化                       │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
                                        │
┌─────────────────────────────────────────────────────────────────────────────────────┐
│                              分层记忆体系                                            │
├─────────────────────────────────────────────────────────────────────────────────────┤
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      L0: 即时记忆 (Redis)                                   │   │
│  │                     实时作战室 - 任务执行期间                                │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    Redis Hash结构                                   │    │   │
│  │  │                  task:{task_id}                                     │    │   │
│  │  │                                                                     │    │   │
│  │  │  • task_id, user_id, status                # 基础信息               │    │   │
│  │  │  • progress, current_step_message          # 进度信息               │    │   │
│  │  │  • raw_user_query, parsed_intent           # 查询信息               │    │   │
│  │  │  • user_profile_snapshot                   # 用户画像快照           │    │   │
│  │  │  • llm_reasoning_trace                     # LLM推理链              │    │   │
│  │  │  • tool_calls_log                          # 工具调用日志           │    │   │
│  │  │  • poi_candidates, selected_pois           # POI选择过程            │    │   │
│  │  │  • interactive_choice_request/response     # 交互确认               │    │   │
│  │  │  • final_result, error_message             # 最终结果               │    │   │
│  │  │  • TTL: 30分钟                             # 自动过期               │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      L1: 短期记忆 (Redis)                                   │   │
│  │                    会话期间的上下文记忆                                      │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    会话上下文缓存                                    │    │   │
│  │  │                                                                     │    │   │
│  │  │  • 用户画像和偏好的会话级缓存                                       │    │   │
│  │  │  • 当前任务相关的历史记忆片段                                       │    │   │
│  │  │  • 工作流状态和中间结果                                             │    │   │
│  │  │  • 从L2记忆系统检索的相关信息                                       │    │   │
│  │  │  • TTL: 会话结束后清理                                              │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      L2: 长期记忆 (MySQL + MongoDB)                         │   │
│  │                      永久档案馆 - 持久化存储                                │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    MySQL结构化记忆                                  │    │   │
│  │  │                                                                     │    │   │
│  │  │  ┌─────────────────────────────────────────────────────────────┐    │    │   │
│  │  │  │                  ai_planning_sessions                       │    │    │   │
│  │  │  │                                                             │    │    │   │
│  │  │  │  • task_id (主键)                                           │    │    │   │
│  │  │  │  • user_id, status, created_at, updated_at                 │    │    │   │
│  │  │  │  • planning_type, result_summary                           │    │    │   │
│  │  │  │  • 任务状态跟踪和统计分析                                   │    │    │   │
│  │  │  └─────────────────────────────────────────────────────────────┘    │    │   │
│  │  │                                                                     │    │   │
│  │  │  ┌─────────────────────────────────────────────────────────────┐    │    │   │
│  │  │  │                  user_memories                              │    │    │   │
│  │  │  │                                                             │    │    │   │
│  │  │  │  • memory_id (主键), user_id                                │    │    │   │
│  │  │  │  • memory_type, content, relevance_score                   │    │    │   │
│  │  │  │  • created_at, last_accessed, access_count                 │    │    │   │
│  │  │  │  • 用户偏好和行为模式记忆                                   │    │    │   │
│  │  │  └─────────────────────────────────────────────────────────────┘    │    │   │
│  │  │                                                                     │    │   │
│  │  │  ┌─────────────────────────────────────────────────────────────┐    │    │   │
│  │  │  │                  user_summaries                             │    │    │   │
│  │  │  │                                                             │    │    │   │
│  │  │  │  • user_id (主键)                                           │    │    │   │
│  │  │  │  • profile_summary, preference_tags                        │    │    │   │
│  │  │  │  • travel_history_summary                                  │    │    │   │
│  │  │  │  • updated_at, version                                     │    │    │   │
│  │  │  │  • 用户画像摘要和快速检索                                   │    │    │   │
│  │  │  └─────────────────────────────────────────────────────────────┘    │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                  MongoDB非结构化记忆                                │    │   │
│  │  │                                                                     │    │   │
│  │  │  ┌─────────────────────────────────────────────────────────────┐    │    │   │
│  │  │  │                ai_interaction_logs                          │    │    │   │
│  │  │  │                                                             │    │    │   │
│  │  │  │  • task_id, user_id, status                                 │    │    │   │
│  │  │  │  • complete_execution_log (从Redis归档)                     │    │    │   │
│  │  │  │  • user_interactions, llm_decisions                        │    │    │   │
│  │  │  │  • tool_usage_details, error_traces                        │    │    │   │
│  │  │  │  • 完整的"黑匣子"交互日志                                   │    │    │   │
│  │  │  └─────────────────────────────────────────────────────────────┘    │    │   │
│  │  │                                                                     │    │   │
│  │  │  ┌─────────────────────────────────────────────────────────────┐    │    │   │
│  │  │  │                user_memories                                │    │    │   │
│  │  │  │                                                             │    │    │   │
│  │  │  │  • memory_id, user_id, memory_type                          │    │    │   │
│  │  │  │  • content, context, importance_score                      │    │    │   │
│  │  │  │  • tags, related_itinerary_id                              │    │    │   │
│  │  │  │  • created_at, accessed_at, access_count                   │    │    │   │
│  │  │  │  • 动态记忆和学习要点存储                                   │    │    │   │
│  │  │  └─────────────────────────────────────────────────────────────┘    │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
│                                        │                                           │
│  ┌─────────────────────────────────────────────────────────────────────────────┐   │
│  │                      记忆管理系统                                           │   │
│  │                   (Memory Manager)                                         │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    记忆存储管理                                      │    │   │
│  │  │                                                                     │    │   │
│  │  │  • store_memory()                  # 存储新记忆                     │    │   │
│  │  │  • get_relevant_memories()         # 检索相关记忆                   │    │   │
│  │  │  • update_memory_access()          # 更新记忆访问记录               │    │   │
│  │  │  • get_user_preferences()          # 获取用户偏好摘要               │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    记忆学习系统                                      │    │   │
│  │  │                                                                     │    │   │
│  │  │  • learn_from_interaction()        # 从交互中学习                   │    │   │
│  │  │  • _extract_learning_points()      # 提取学习要点                   │    │   │
│  │  │  • _calculate_relevance()          # 计算记忆相关性评分             │    │   │
│  │  │  • _calculate_content_relevance()  # 计算内容相关性                 │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  │                                                                             │   │
│  │  ┌─────────────────────────────────────────────────────────────────────┐    │   │
│  │  │                    记忆服务层                                        │    │   │
│  │  │                  (Memory Service)                                  │    │   │
│  │  │                                                                     │    │   │
│  │  │  • get_user_memories()             # 获取用户记忆(含缓存)           │    │   │
│  │  │  • save_memory()                   # 保存记忆                       │    │   │
│  │  │  • update_memory()                 # 更新记忆                       │    │   │
│  │  │  • delete_memory()                 # 删除记忆                       │    │   │
│  │  │  • 多层缓存策略和性能优化                                           │    │   │
│  │  └─────────────────────────────────────────────────────────────────────┘    │   │
│  └─────────────────────────────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────────────────────────────┘
```

## 系统特性说明

### 1. LangGraph工作流引擎

- **状态图架构**: 基于LangGraph的StateGraph实现，支持复杂的条件分支和并行处理
- **检查点机制**: 使用MemorySaver实现工作流状态的自动保存和恢复
- **灵活路由**: 支持跳过分析阶段直接进入规划，或独立执行分析阶段
- **错误处理**: 完善的错误捕获和处理机制，确保系统稳定性

### 2. 双模式运行支持

- **全自动模式**: `plan_travel_automatic()` - 完全自动化的旅行规划
- **流式自动模式**: `plan_travel_stream_automatic()` - 带实时进度反馈的自动规划
- **交互模式**: `plan_travel_stream_interactive()` - 支持用户交互确认的规划模式

### 3. 分层记忆体系

- **L0即时记忆**: Redis Hash存储任务执行期间的实时状态，TTL 30分钟
- **L1短期记忆**: 会话期间的上下文缓存，从L2检索的相关信息
- **L2长期记忆**: MySQL存储结构化用户画像，MongoDB存储完整交互日志

### 4. 智能状态管理

- **TravelPlanState**: 完整的状态定义，包含用户信息、分析结果、规划结果
- **事件驱动**: 基于事件的状态更新和进度跟踪
- **状态持久化**: 自动状态保存和恢复，支持任务中断后继续执行

### 5. 工具集成生态

- **高德地图MCP工具**: 完整的地理服务、路线规划、信息查询工具集
- **分析服务**: 专业的意图分析、策略制定、偏好分析服务
- **记忆管理**: 智能的记忆存储、检索、学习系统

## 数据流转机制

### 任务初始化 (三方占坑)
1. 生成全局唯一task_id
2. MySQL插入任务记录 (ai_planning_sessions)
3. MongoDB创建骨架文档 (ai_interaction_logs)
4. Redis创建实时作战室 (task:{task_id})

### 任务执行 (实时处理)
1. LangGraph工作流启动，状态存储在MemorySaver
2. 各节点实时更新Redis中的任务状态
3. SSE推送进度更新到前端
4. 记忆系统提供上下文支持

### 任务完成 (归档学习)
1. Redis数据归档到MongoDB
2. MySQL更新任务状态
3. 记忆系统学习用户偏好
4. 清理Redis临时数据

## 核心优势

1. **高性能**: Redis作为实时作战室，确保毫秒级响应
2. **可扩展**: LangGraph状态图支持复杂工作流扩展
3. **智能化**: 分层记忆体系支持个性化和持续学习
4. **可靠性**: 完善的错误处理和状态恢复机制
5. **实时性**: SSE流式输出提供实时用户体验