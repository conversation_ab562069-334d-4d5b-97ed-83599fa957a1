from sqlalchemy import (
    Column, Integer, BigInteger, String, Text, Boolean, DateTime, Enum, ForeignKey, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.dialects.mysql import JSON as MySQLJSON
import enum

Base = declarative_base()

class GenerationSourceEnum(enum.Enum):
    PRESET = "PRESET"
    UGC = "UGC"

class AiSkill(Base):
    __tablename__ = "ai_skills"
    __table_args__ = {'schema': 'dh_ailab'}
    id = Column(Integer, primary_key=True, autoincrement=True)
    skill_key = Column(String(50), unique=True, nullable=False, comment="技能的唯一英文标识 (程序中使用)")
    category_id = Column(Integer, comment="逻辑外键, 关联到 ai_skill_categories.id")
    name = Column(String(50), nullable=False, comment="技能名称 (用于列表等短文本场景)")
    content = Column(MySQLJSON, nullable=False, comment="技能的完整结构化内容，用于详情页展示")
    unlock_cost_points = Column(Integer, nullable=False, default=0, comment="解锁该技能所需消耗的积分")
    is_public = Column(Boolean, nullable=False, default=True, comment="是否对所有用户可见")

class AiSkillCategory(Base):
    __tablename__ = "ai_skill_categories"
    __table_args__ = {'schema': 'dh_ailab'}
    id = Column(Integer, primary_key=True, autoincrement=True)
    category_key = Column(String(50), unique=True, nullable=False, comment="类别的唯一英文标识")
    name = Column(String(50), nullable=False, comment="类别名称，用于UI显示")
    display_order = Column(Integer, nullable=False, default=0, comment="显示顺序，值越小越靠前")

class StatusDefinition(Base):
    __tablename__ = "status_definitions"
    __table_args__ = (
        UniqueConstraint('status_key', 'scope', name='idx_key_scope_unique'),
        {'schema': 'dh_ailab'}
    )
    id = Column(Integer, primary_key=True, autoincrement=True)
    status_key = Column(String(50), nullable=False, comment="状态的英文键")
    scope = Column(String(50), nullable=False, comment="作用域 (例如: VPA_INSTANCE)")
    display_name = Column(String(50), nullable=False, comment="状态的中文显示名")

class UserUnlockedSkill(Base):
    __tablename__ = "user_unlocked_skills"
    __table_args__ = (
        UniqueConstraint('user_id', 'skill_id', name='idx_user_skill_unique'),
        {'schema': 'dh_ailab'}
    )
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 dh_user_profile.users.id")
    skill_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 ai_skills.id")
    is_enabled = Column(Boolean, nullable=False, default=True, comment="用户是否开启此技能的主动服务")
    unlocked_at = Column(DateTime, nullable=False, comment="技能解锁时间")

class UserVpaInstance(Base):
    __tablename__ = "user_vpa_instances"
    __table_args__ = {'schema': 'dh_ailab'}
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 dh_user_profile.users.id")
    nickname = Column(String(50), nullable=False, comment="用户为该形象实例设置的昵称")
    status_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 status_definitions.id (scope=\"VPA_INSTANCE\")")
    is_currently_active = Column(Boolean, nullable=False, default=False, comment="是否为用户当前正在使用的形象")
    asset_id = Column(String(100), comment="关联到远端AI资产库的数字人资产ID")
    actions = Column(MySQLJSON, comment="该形象实例拥有的一套动作集 (JSON对象, key:动作类型, value:动作资产ID)")
    thumbnail_url = Column(String(255), comment="该实例的静态缩略图URL")
    custom_wake_word = Column(String(20), comment="自定义唤醒词")
    custom_response = Column(String(50), comment="自定义应答语")
    generation_source = Column(Enum(GenerationSourceEnum), nullable=False, default='UGC', comment="来源 (PRESET:系统预设, UGC:用户生成)")
    base_persona_id = Column(Integer, comment="如果来源是PRESET, 关联到 vpa_personas.id")
    generation_style_id = Column(Integer, comment="如果来源是UGC, 逻辑外键, 关联到 vpa_generation_styles.id")
    generation_prompt = Column(Text, comment="如果来源是UGC, 记录当时用户输入的Prompt")
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

class UserVpaRoleSkill(Base):
    __tablename__ = "user_vpa_role_skills"
    __table_args__ = (
        UniqueConstraint('user_vpa_role_id', 'skill_id', name='idx_user_role_skill_unique'),
        {'schema': 'dh_ailab'}
    )
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_vpa_role_id = Column(BigInteger, nullable=False, comment="逻辑外键, 关联到 user_vpa_roles.id")
    skill_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 vpa_skills.id")
    is_enabled = Column(Boolean, nullable=False, default=True, comment="用户是否启用该技能")
    skill_level = Column(Integer, nullable=False, default=1, comment="用户设置的技能等级 (1-5)")
    custom_trigger = Column(String(100), comment="用户自定义的触发条件")
    added_at = Column(DateTime, nullable=False, comment="技能添加时间")

class VpaGenerationStyle(Base):
    __tablename__ = "vpa_generation_styles"
    __table_args__ = {'schema': 'dh_ailab'}
    id = Column(Integer, primary_key=True, autoincrement=True)
    style_key = Column(String(50), unique=True, nullable=False, comment="风格的唯一英文标识")
    name = Column(String(50), nullable=False, comment="风格名称，用于UI显示")
    description = Column(Text, comment="该风格的详细描述或默认的Prompt提示词")
    is_available = Column(Boolean, nullable=False, default=True, comment="该风格当前是否可用")

class VpaRole(Base):
    __tablename__ = "vpa_roles"
    __table_args__ = {'schema': 'dh_ailab'}
    id = Column(Integer, primary_key=True, autoincrement=True)
    role_key = Column(String(50), unique=True, nullable=False, comment="角色的唯一英文标识")
    name = Column(String(50), nullable=False, comment="角色名称，用于UI显示")
    description = Column(Text, comment="角色的详细描述")
    role_type = Column(String(30), nullable=False, comment="角色类型 (例如: assistant, entertainer, teacher)")
    base_asset_id = Column(String(100), nullable=False, comment="角色的基础资产ID（外观、声音等）")
    thumbnail_url = Column(String(255), nullable=False, comment="角色的缩略图URL")
    is_available = Column(Boolean, nullable=False, default=True, comment="该角色当前是否可用")
    created_at = Column(DateTime, nullable=False)

class VpaRoleSkill(Base):
    __tablename__ = "vpa_role_skills"
    __table_args__ = (
        UniqueConstraint('role_id', 'skill_id', name='idx_role_skill_unique'),
        {'schema': 'dh_ailab'}
    )
    id = Column(Integer, primary_key=True, autoincrement=True)
    role_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 vpa_roles.id")
    skill_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 vpa_skills.id")
    is_default = Column(Boolean, nullable=False, default=False, comment="是否为该角色的默认技能")
    skill_level = Column(Integer, nullable=False, default=1, comment="技能等级 (1-5)")
    display_order = Column(Integer, nullable=False, default=0, comment="在该角色中的显示顺序")

class VpaSkill(Base):
    __tablename__ = "vpa_skills"
    __table_args__ = {'schema': 'dh_ailab'}
    id = Column(Integer, primary_key=True, autoincrement=True)
    skill_key = Column(String(50), unique=True, nullable=False, comment="技能的唯一英文标识")
    name = Column(String(50), nullable=False, comment="技能名称，用于UI显示")
    description = Column(Text, comment="技能的详细描述")
    skill_type = Column(String(30), nullable=False, comment="技能类型 (例如: action, expression, interaction)")
    external_asset_id = Column(String(100), nullable=False, comment="关联到外部资产库的技能资产ID")
    duration_ms = Column(Integer, comment="技能执行持续时间（毫秒）")
    trigger_conditions = Column(MySQLJSON, comment="技能触发条件（JSON格式）")
    is_available = Column(Boolean, nullable=False, default=True, comment="该技能当前是否可用")
    created_at = Column(DateTime, nullable=False)

class UserVpaRole(Base):
    __tablename__ = "user_vpa_roles"
    __table_args__ = (
        UniqueConstraint('user_id', 'role_id', name='idx_user_role_unique'),
        {'schema': 'dh_ailab'}
    )
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=False, comment="外键, 关联到 dh_user_profile.users.id (用户档案系统)")
    role_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 vpa_roles.id (用户选择的VPA角色)")
    nickname = Column(String(50), nullable=False, comment="用户为该VPA角色设置的昵称")
    is_currently_active = Column(Boolean, nullable=False, default=False, comment="是否为用户当前正在使用的VPA角色")
    custom_asset_id = Column(String(100), comment="用户自定义的资产ID，如果为空则使用角色的base_asset_id")
    custom_wake_word = Column(String(20), comment="自定义唤醒词")
    custom_response = Column(String(50), comment="自定义应答语")
    memory_context = Column(MySQLJSON, comment="与该VPA角色相关的记忆上下文配置")
    personality_traits = Column(MySQLJSON, comment="基于用户记忆定制的个性化特征")
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

class VpaSeqLog(Base):
    __tablename__ = "vpa_seq_logs"
    __table_args__ = {'schema': 'dh_ailab'}
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    instance_id = Column(BigInteger, nullable=False, comment="表user_vpa_instances.id 或者user_vpa_gif_instances.id")
    task_id = Column(BigInteger, nullable=False)
    seq_id = Column(String(100), nullable=False)
    status_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 status_definitions.id (scope=\"VPA_INSTANCE\")")
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)
    result_url = Column(String(255), comment="图片地址或视频地址")

class UserVpaGifInstance(Base):
    __tablename__ = "user_vpa_gif_instances"
    __table_args__ = (
        UniqueConstraint('vpa_id', 'action_id', name='user_vpa_gif_instances_unique'),
        {'schema': 'dh_ailab'}
    )
    id = Column(BigInteger, primary_key=True, autoincrement=True)
    vpa_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 表user_vpa_instances.id")
    action_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 vpa_action_prompts.id")
    status_id = Column(Integer, nullable=False, comment="逻辑外键, 关联到 status_definitions.id (scope=\"VPA_INSTANCE\")")
    is_currently_active = Column(Boolean, nullable=False, default=False, comment="是否为用户当前正在使用的形象")
    asset_id = Column(String(100), comment="关联到远端AI资产库的数字人资产ID")
    thumbnail_url = Column(String(255), comment="该实例的静态缩略图URL")
    image_url = Column(String(255), comment="该实例的二次生图URL")
    gif_url = Column(String(255), comment="该实例的动作gif URL")
    created_at = Column(DateTime, nullable=False)
    updated_at = Column(DateTime, nullable=False)

class VpaActionPrompt(Base):
    __tablename__ = "vpa_action_prompts"
    __table_args__ = {'schema': 'dh_ailab'}
    id = Column(Integer, primary_key=True, autoincrement=True)
    action_category = Column(String(10), nullable=False, comment="一级分类")
    action_type = Column(String(50), nullable=False, comment="二级分类")
    description = Column(String(255), comment="二次图修改提示词")
    action_prompt = Column(String(255), comment="动作prompt")

# VPA_INSTANCE 状态常量
class VpaInstanceStatus:
    ACTIVE = 1         # 可用
    GENERATING = 2     # 制作中
    FAILED = 3         # 制作失败
    ARCHIVED = 4       # 已归档
