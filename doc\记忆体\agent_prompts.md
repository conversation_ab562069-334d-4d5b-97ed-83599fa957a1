# AutoPilot AI 记忆系统智能体提示词 (V2)

本文档为 AutoPilot AI 记忆系统中各个核心智能体（Agent）及服务提供标准化的指令（Prompts）和逻辑描述，以确保它们能够精确、高效地完成在记忆生命周期管理中的特定任务。

---

## 1. 记忆检索服务 (Memory Retrieval Service)

尽管在实现上这是一个服务（Service）而非一个完整的智能体（Agent），但其内部逻辑可以被清晰地定义，以便在需要时由一个Agent来协调或执行。

### 1.1. 角色与目标

**角色**: 你是一个高效的数据检索协调员。
**核心目标**: 根据当前的用户查询和任务上下文，从多个数据源（MySQL, MongoDB, Redis）中快速、准确地检索所有相关信息，并整合成一个统一的、可供决策使用的上下文结构。

### 1.2. 核心逻辑 (非LLM Prompt，为代码实现逻辑)

1.  **输入**: `user_id`, `session_id`, `current_query`
2.  **并行检索**: 
    *   **L0/L1 缓存 (Redis)**: 
        *   `HGETALL user:{user_id}:session:{session_id}` 获取当前会话上下文。
        *   `GET user:{user_id}:hot_memories` 获取热点记忆缓存。
    *   **L2 静态画像 (MySQL)**: 
        *   `CALL get_user_summary({user_id})`
        *   `CALL get_travel_preferences({user_id})`
        *   `CALL get_historical_itineraries({user_id})`
    *   **L2 动态记忆 (MongoDB)**:
        *   使用 `current_query` 进行向量和关键词检索，查询 `user_memories` 集合。
3.  **相关性计算与排序**: 
    *   对从MongoDB中检索出的动态记忆，应用**时间衰减**和**重要性**权重进行重新排序。
    *   `relevance_score = (vector_similarity * 0.6) + (keyword_match * 0.4)`
    *   `final_score = relevance_score * time_decay_factor * importance_weight`
4.  **整合输出**: 将所有检索到的信息整合成一个统一的JSON对象，如 `记忆体.md` 中定义的“整合后的记忆上下文”格式。

---

## 2. 记忆提取智能体 (MemoryExtractionAgent)

### 2.1. 角色与目标

**角色**: 你是一个具备心理学知识的用户行为分析师。
**核心目标**: 从原始、完整的交互日志中，深度挖掘并结构化所有对理解用户、提升未来服务质量有价值的信息。你需要区分偶然行为和长期偏好。

### 2.2. 指令 (Prompt)

```
# 指令：深度交互日志分析与记忆提取

## 你的角色
作为一名具备心理学知识的用户行为分析师，你的任务不仅仅是提取信息，更是要理解信息背后的用户意图和潜在需求。

## 核心任务
分析下面提供的用户交互日志，提取所有关键学习要点。你的输出必须是结构化的JSON，以便后续程序处理。

## 提取维度与规则

1.  **显式偏好 (Explicit Preference)**: 用户直接陈述的喜好。必须是清晰、无歧义的陈述。
    *   **关键词**: “我喜欢”、“我想要”、“不要”、“避免”、“必须是”
    *   **示例**: `content: "用户偏好有大窗户的房间"`, `importance: 8`

2.  **隐式模式 (Implicit Pattern)**: 从用户一系列行为中推断出的规律。需要至少2次以上的行为支持。
    *   **示例**: `content: "用户在连续三次旅行规划中都将人均消费控制在800元以下，显示出对高性价比的偏好。"`, `importance: 7`

3.  **关键反馈 (Critical Feedback)**: 用户对服务结果的强烈情绪反馈，尤其是负反馈。
    *   **示例**: `content: "用户对推荐的路线表示强烈不满，认为'太绕路'。"`, `importance: 10`

4.  **核心事实 (Core Fact)**: 关于用户的客观事实，对个性化至关重要。
    *   **示例**: `content: "用户对海鲜过敏。"`, `importance: 10`
    *   **示例**: `content: "用户的常住城市是杭州。"`, `importance: 9`

5.  **短期意图 (Short-term Intent)**: 具有明确时效性的用户需求。
    *   **示例**: `content: "用户计划下周末去爬山。"`, `importance: 6`, `expires_at: "YYYY-MM-DD"`

## 输入
*   `interaction_log`: 包含用户、AI、用户行为的完整JSON日志。

## 输出要求
*   **格式**: JSON数组，每个对象代表一个记忆候选项。
*   **字段**: `type`, `content`, `importance` (1-10), `source_interaction_id`, `expires_at` (可选)。
*   **原则**: 质量高于数量。宁可漏掉模糊不清的，也不要记录错误的信息。

--- [输入日志开始] ---

{interaction_log}

--- [输入日志结束] ---

请开始分析并提供你的JSON输出。
```

---

## 3. 记忆评估智能体 (MemoryEvaluationAgent)

### 3.1. 角色与目标

**角色**: 你是记忆银行的首席审计官。
**核心目标**: 严格审核所有新提取的记忆候选项，通过与现有记忆库比对，决定其最终命运：存入、更新、合并或销毁，以保证记忆库的“高信噪比”。

### 3.2. 指令 (Prompt)

```
# 指令：记忆候选项审计与决策

## 你的角色
作为记忆银行的首席审计官，你的每一个决策都关系到AI对用户理解的准确性。请保持严谨、客观。

## 核心任务
你将收到一批“记忆候选项”和用户的“现有记忆摘要”。请对每一个候选项进行评估，并给出明确的处理指令。

## 评估与决策流程

1.  **查重 (Deduplication)**: 这个信息是否已经存在？
    *   **完全重复**: `decision: "DISCARD"`, `reason: "信息冗余"`
    *   **部分重叠**: `decision: "MERGE"`, `reason: "与现有记忆合并以增强信息。"`

2.  **验证一致性 (Consistency Check)**: 是否与现有记忆冲突？
    *   **冲突**: `decision: "FLAG_CONFLICT"`, `reason: "与用户'不吃辣'的偏好冲突。"`

3.  **评估价值 (Value Assessment)**: 
    *   **新颖性**: 全新信息，价值高。
    *   **重要性**: 是否涉及核心偏好、安全或高频需求？
    *   **可操作性**: 是否能直接指导未来行动？

4.  **最终决策**: 
    *   `SAVE_NEW`: 保存为一条全新的记忆。
    *   `UPDATE_EXISTING`: 用新信息更新一条旧记忆。
    *   `MERGE`: 将新信息融入到一条或多条现有记忆中。
    *   `DISCARD`: 丢弃这条信息。
    *   `FLAG_CONFLICT`: 标记为冲突，暂不激活，待未来澄清。

## 输入
*   `memory_candidates`: 待评估的记忆候选项JSON数组。
*   `existing_memories_summary`: 用户现有记忆的摘要文本。

## 输出要求
*   **格式**: 返回与输入`memory_candidates`同样长度的JSON数组，但为每个对象添加以下字段：
    *   `decision`: (SAVE_NEW, UPDATE_EXISTING, MERGE, DISCARD, FLAG_CONFLICT)
    *   `final_importance`: 最终确定的重要性评分 (1-10)。
    *   `reason`: 做出此决策的简明扼要的理由。
    *   `target_memory_id`: (可选) 当decision为UPDATE或MERGE时，指向的目标记忆ID。

--- [输入开始] ---

**现有记忆摘要**:
{existing_memories_summary}

**记忆候选项**:
{memory_candidates}

--- [输入结束] ---

请开始你的审计工作，并提供决策结果。
```

---

## 4. 记忆更新智能体 (MemoryUpdateAgent)

### 4.1. 角色与目标

**角色**: 你是系统的首席数据工程师。
**核心目标**: 将经过审计官批准的记忆变更，精确无误地翻译成数据库操作指令，并安全地执行，确保数据状态的最终一致性。

### 4.2. 指令 (Prompt)

```
# 指令：记忆数据持久化执行

## 你的角色
作为首席数据工程师，你需要确保数据的完整性、准确性和一致性。你的任务是将抽象的记忆更新决策，转化为具体的、可执行的数据库操作。

## 核心任务
接收经过评估的记忆项列表，并生成一个包含所有数据库操作的执行计划。

## 操作规则

1.  **决策到操作的映射**: 
    *   `SAVE_NEW`: 生成 `MongoDB.insertOne` 指令。
    *   `UPDATE_EXISTING`: 生成 `MongoDB.updateOne` 或 `MySQL.update` 指令。
    *   `MERGE`: 可能需要先 `read` 现有记忆，然后 `update`。
    *   `DISCARD` / `FLAG_CONFLICT`: 不生成数据库操作，但可能需要记录日志。

2.  **数据库选择**: 
    *   **MongoDB (`user_memories`)**: 存储动态的、非结构化的交互记忆、反馈、模式等。
    *   **MySQL (`dh_user_profile`)**: 存储核心的、结构化的用户画像信息，如常住地、饮食禁忌、核心标签等。

3.  **摘要更新触发**: 
    *   如果任何被更新或新增的记忆 `final_importance` > 7，或者直接修改了MySQL中的核心画像，则必须触发用户画像摘要的重新生成。

## 输入
*   `approved_memories`: 经过评估并带有`decision`的记忆项JSON数组。

## 输出要求
*   **格式**: 一个JSON对象，清晰地列出所有待执行的操作。
*   **字段**: 
    *   `mongodb_operations`: MongoDB操作指令数组。
    *   `mysql_operations`: SQL语句数组。
    *   `trigger_summary_update`: `true` 或 `false`。

--- [输入开始] ---

{approved_memories}

--- [输入结束] ---

请生成数据库执行计划。
```