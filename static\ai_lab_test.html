<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>AI LAB 数字人体验</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        h1 { margin-bottom: 30px; }
        .container { display: flex; gap: 40px; }
        .vpa-list { flex: 2; }
        .vpa-card { background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #eee; margin-bottom: 20px; padding: 20px; }
        .vpa-card h3 { margin: 0 0 10px 0; }
        .vpa-card .thumb { width: 120px; height: 120px; object-fit: cover; border-radius: 6px; border: 1px solid #eee; }
        .gif-list { display: flex; gap: 10px; flex-wrap: wrap; margin-top: 10px; }
        .gif-item { text-align: center; }
        .gif-thumb { width: 80px; height: 80px; object-fit: cover; border-radius: 4px; border: 1px solid #ddd; }
        .logs { font-size: 12px; color: #666; margin-top: 10px; background: #f4f4f4; border-radius: 4px; padding: 6px; }
        .form-section { flex: 1; background: #fff; border-radius: 8px; box-shadow: 0 2px 8px #eee; padding: 20px; }
        label { display: block; margin-top: 12px; }
        input, textarea, select { width: 100%; margin-top: 4px; padding: 6px; border-radius: 4px; border: 1px solid #ccc; }
        button { margin-top: 16px; padding: 8px 18px; border: none; border-radius: 4px; background: #007bff; color: #fff; cursor: pointer; }
        button:disabled { background: #aaa; }
        .actions { margin-top: 10px; }
        .small { font-size: 12px; color: #888; }
    </style>
</head>
<body>
    <h1>AI LAB 数字人体验</h1>
    <div class="container">
        <div class="vpa-list" id="vpa_list">
            <!-- VPA卡片列表 -->
        </div>
        <div class="form-section">
            <h2>新建数字人</h2>
            <form id="create_vpa_form" onsubmit="return createVpa(event)">
                <label>用户ID <input id="new_user_id" value="1" required /></label>
                <label>昵称 <input id="new_nickname" placeholder="可选" /></label>
                <label>形象描述（prompt）<textarea id="new_prompt" required>一个可爱的机器人</textarea></label>
                <button type="submit">创建数字人</button>
            </form>
            <hr style="margin:30px 0;">
            <h2>为数字人添加动作</h2>
            <form id="add_action_form" onsubmit="return addAction(event)">
                <label>VPA ID <input id="action_vpa_id" required /></label>
                <label>选择动作：</label>
                <div id="action_checkbox_group" style="margin-bottom:10px;"></div>
                <input type="hidden" id="action_action_ids" required />
                <label>控制类型 <select id="action_controller_type"><option value="canny">canny</option><option value="depth">depth</option><option value="pose">pose</option></select></label>
                <label>强度 <input id="action_strength" value="0.6" /></label>
                <label>缩放 <input id="action_scale" value="3.0" /></label>
                <label>时长 <input id="action_duration" value="5" /></label>
                <button type="submit">添加动作</button>
            </form>
        </div>
    </div>
    <script>
        let allActions = [];
        async function fetchActionList() {
            const res = await fetch('/api/ai_lab/action_prompt_list');
            allActions = await res.json();
            renderActionCheckboxes();
        }
        function renderActionCheckboxes() {
            const group = document.getElementById('action_checkbox_group');
            if (!allActions.length) { group.innerHTML = '<span>无可用动作</span>'; return; }
            group.innerHTML = allActions.map(a => `
                <label style="display:inline-block;margin-right:12px;">
                    <input type="checkbox" class="action-checkbox" value="${a.id}" checked />
                    ${a.action_type || ('动作'+a.id)}
                </label>
            `).join('');
            syncActionIds();
            document.querySelectorAll('.action-checkbox').forEach(cb => {
                cb.addEventListener('change', syncActionIds);
            });
        }
        function syncActionIds() {
            const checked = Array.from(document.querySelectorAll('.action-checkbox:checked')).map(cb => cb.value);
            document.getElementById('action_action_ids').value = checked.join(',');
        }
        async function fetchVpaList() {
            const res = await fetch('/api/ai_lab/vpa_list');
            const data = await res.json();
            renderVpaList(data);
        }
        function renderVpaList(list) {
            const el = document.getElementById('vpa_list');
            if (!list.length) { el.innerHTML = '<div>暂无数字人</div>'; return; }
            el.innerHTML = list.map(vpa => `
                <div class="vpa-card">
                    <h3>${vpa.nickname || '未命名'} <span class="small">(ID:${vpa.id})</span></h3>
                    <div style="display:flex;align-items:center;gap:20px;">
                        <img class="thumb" src="${vpa.thumbnail_url || ''}" alt="thumb" onerror="this.src=''" />
                        <div>
                            <div><b>描述:</b> ${vpa.generation_prompt || ''}</div>
                            <div><b>状态:</b> ${vpa.status_id} <b>创建:</b> ${vpa.created_at?.slice(0,19).replace('T',' ')}</div>
                        </div>
                    </div>
                    <div class="actions">
                        <button onclick="showAddAction(${vpa.id})">添加动作</button>
                        <button onclick="refreshVpaList()">刷新</button>
                    </div>
                    <div style="margin-top:10px;">
                        <b>动作与GIF：</b>
                        <div class="gif-list">
                        ${vpa.gif_instances.map(gif => `
                            <div class="gif-item">
                                <div>${gif.action_type || ''}</div>
                                <img class="gif-thumb" src="${gif.gif_url || gif.thumbnail_url || ''}" alt="gif" onerror="this.src=''" />
                                <div class="small">状态:${gif.status_id}</div>
                            </div>
                        `).join('')}
                        </div>
                    </div>
                    <div class="logs">
                        <b>日志：</b>
                        ${vpa.logs.map(log => `
                            <div>[${log.created_at?.slice(0,19).replace('T',' ')}] 状态:${log.status_id} 任务:${log.task_id||''} 序列:${log.seq_id||''}</div>
                        `).join('')}
                    </div>
                </div>
            `).join('');
        }
        function showAddAction(vpaId) {
            document.getElementById('action_vpa_id').value = vpaId;
            window.scrollTo({top:0,left:0,behavior:'smooth'});
            fetch('/api/ai_lab/actions')
            .then(res => res.json())
            .then(actions => {
                const group = document.getElementById('action_checkbox_group');
                group.innerHTML = '';
                actions.forEach(action => {
                    const label = document.createElement('label');
                    label.style="display:inline-flex;align-items:center;gap:8px;margin-right:16px;";
                    label.innerHTML = `
                        <input type="checkbox" value="${action.id}" checked /> ${action.name}
                    `;
                    group.appendChild(label);
                });
            });
        }
        async function createVpa(e) {
            e.preventDefault();
            const data = {
                prompt: document.getElementById('new_prompt').value,
                user_id: Number(document.getElementById('new_user_id').value),
                nickname: document.getElementById('new_nickname').value || undefined
            };
            const btn = e.target.querySelector('button[type=submit]');
            btn.disabled = true;
            btn.innerText = '创建中...';
            try {
                const res = await fetch('/api/ai_lab/text_to_img', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await res.json();
                alert('创建成功，VPA ID: ' + result.ids.join(','));
                fetchVpaList();
            } catch (err) { alert('创建失败'); }
            btn.disabled = false;
            btn.innerText = '创建数字人';
        }
        async function addAction(e) {
            e.preventDefault();
            const vpa_id = Number(document.getElementById('action_vpa_id').value);
            const action_ids = Array.from(document.querySelectorAll('#action_checkbox_group input:checked')).map(el => Number(el.value));
            const data = {
                user_id: 1, // 可根据需要调整
                vpa_id,
                action_ids,
                controller_type: document.getElementById('action_controller_type').value,
                strength: Number(document.getElementById('action_strength').value),
                scale: Number(document.getElementById('action_scale').value),
                duration: Number(document.getElementById('action_duration').value)
            };
            const btn = e.target.querySelector('button[type=submit]');
            btn.disabled = true;
            btn.innerText = '添加中...';
            try {
                const res = await fetch('/api/ai_lab/generate_avatar', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                const result = await res.json();
                alert('添加成功，任务ID: ' + result.task_ids.join(','));
                fetchVpaList();
            } catch (err) { alert('添加失败'); }
            btn.disabled = false;
            btn.innerText = '添加动作';
        }
        function refreshVpaList() { fetchVpaList(); }
        window.onload = function() { fetchActionList(); fetchVpaList(); };
    </script>
</body>
</html>
