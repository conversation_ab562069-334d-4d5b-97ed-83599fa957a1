"""
Web application for testing tools without AutoGen integration
"""
import os
import requests
import argparse
import asyncio
from fastapi import APIRouter, Request, Body
from tools.Amap.map_tool import MapTool, Location
from tools.NetEaseCloudMusic.music_tool import MusicTool

router = APIRouter(prefix="/api/music", tags=["音乐"])
# app = Flask(__name__)

# Initialize tools with API keys from environment variables
map_tool = MapTool()

music_tool = MusicTool()

# Map Tool Routes
@router.post('/map/search_pois')
async def search_pois(data: dict = Body(...)):
    try:
        keywords = data.get('keywords')
        city = data.get('city')
        type_ = data.get('type')
        location = None
        if data.get('latitude') and data.get('longitude'):
            location = Location(
                latitude=float(data.get('latitude')),
                longitude=float(data.get('longitude'))
            )
        results = map_tool.search_pois(
            keywords=keywords,
            city=city,
            type_=type_,
            location=location,
            radius=int(data.get('radius', 3000))
        )
        return [{
            'id': poi.id,
            'name': poi.name,
            'type': poi.type,
            'address': poi.address,
            'location': {
                'latitude': poi.location.latitude,
                'longitude': poi.location.longitude
            },
            'distance': poi.distance,
            'rating': poi.rating,
            'price': poi.price
        } for poi in results]
    except Exception as e:
        return {"error": str(e)}

@router.post('/map/get_route')
async def get_route(data: dict = Body(...)):
    try:
        origin = Location(
            latitude=float(data['originLat']),
            longitude=float(data['originLng'])
        )
        destination = Location(
            latitude=float(data['destLat']),
            longitude=float(data['destLng'])
        )
        waypoints = None
        if data.get('waypoints'):
            waypoints = [
                Location(
                    latitude=float(wp['latitude']),
                    longitude=float(wp['longitude'])
                )
                for wp in data['waypoints']
            ]
        route = map_tool.get_route(
            origin=origin,
            destination=destination,
            waypoints=waypoints,
            transport_mode=data.get('transportMode', 'driving')
        )
        return route
    except Exception as e:
        return {"error": str(e)}

# LLM and Music Tool Routes
@router.post('/get_keyword')
async def get_keyword(data: dict = Body(...)):
    try:
        prompt = data['prompt']
        messages = [
            {"role": "system", "content": "你是一个音乐推荐助手。用户会描述他想听的音乐类型，你需要根据描述提取一个最相关的关键词用于搜索音乐。只返回关键词，不要其他解释。"},
            {"role": "user", "content": prompt}
        ]
        llm_response = requests.post(
            "http://************:8007/v1/chat/completions",
            json={
                "model": "Qwen2.5-32B-Instruct-AWQ",
                "messages": messages,
                "temperature": 0.7,
                "max_tokens": 50,
            }
        )
        llm_data = llm_response.json()
        if "error" in llm_data:
            raise Exception(f"LLM API Error: {llm_data['error']}")
        keyword = llm_data["choices"][0]["message"]["content"].strip()
        return {"keyword": keyword}
    except Exception as e:
        return {"error": str(e)}

@router.post('/search_songs')
async def search_songs(data: dict = Body(...)):
    try:
        results = music_tool.search_songs(
            keyword=data['keyword']
        )
        return [{
            'id': song.id,
            'name': song.name,
            'artist': song.artist,
            'album': song.album,
            'duration': song.duration,
            'url': song.url
        } for song in results]
    except Exception as e:
        return {"error": str(e)}

@router.post('/search_songs_by_artist')
async def search_songs_by_artist(data: dict = Body(...)):
    try:
        results = music_tool.search_songs_by_artist(
            keyword=data['keyword']
        )
        return [{
            'id': song.id,
            'name': song.name,
            'artist': song.artist,
            'album': song.album,
            'duration': song.duration,
            'url': song.url
        } for song in results]
    except Exception as e:
        return {"error": str(e)}

@router.post('/search_songs_by_artist_and_name')
async def search_songs_by_artist_and_name(data: dict = Body(...)):
    try:
        results = music_tool.search_songs_by_artist_and_name(
            artistName=data['artistName'],
            songName=data['songName']
        )
        return [{
            'id': song.id,
            'name': song.name,
            'artist': song.artist,
            'album': song.album,
            'duration': song.duration,
            'url': song.url
        } for song in results]
    except Exception as e:
        return {"error": str(e)}

@router.post('/search_playlists')
async def search_playlists(data: dict = Body(...)):
    try:
        results = music_tool.search_playlists(
            keyword=data['keyword']
        )
        return [{
            'id': playlist.id,
            'name': playlist.name,
            'description': playlist.description,
            'creator': playlist.creator,
            'song_count': playlist.song_count,
            'play_count': playlist.play_count,
            'songs': [{
                'id': song.id,
                'name': song.name,
                'artist': song.artist,
                'album': song.album,
                'duration': song.duration,
                'url': song.url
            } for song in (playlist.songs or [])]
        } for playlist in results]
    except Exception as e:
        return {"error": str(e)}

@router.post('/search_albums')
async def search_albums(data: dict = Body(...)):
    try:
        results = music_tool.search_albums(
            keyword=data['keyword']
        )
        return [{
            'id': album.id,
            'name': album.name,
            'artist': album.artist,
            'description': album.description
        } for album in results]
    except Exception as e:
        return {"error": str(e)}

@router.post('/search_albums_by_artist')
async def search_albums_by_artist(data: dict = Body(...)):
    try:
        results = music_tool.search_albums_by_artist(
            artistName=data['artistName'],
            albumName=data['albumName']
        )
        return [{
            'id': album.id,
            'name': album.name,
            'artist': album.artist,
            'description': album.description
        } for album in results]
    except Exception as e:
        return {"error": str(e)}

@router.post('/search_artists')
async def search_artists(data: dict = Body(...)):
    try:
        results = music_tool.search_artists(
            keyword=data['keyword']
        )
        return [{
            'id': artist.id,
            'name': artist.name,
            'alias': artist.alias
        } for artist in results]
    except Exception as e:
        return {"error": str(e)}

@router.get('/hot_keywords')
async def hot_keywords():
    try:
        results = music_tool.get_hot_keywords()
        return results
    except Exception as e:
        return {"error": str(e)}

@router.post('/suggest_keywords')
async def suggest_keywords(data: dict = Body(...)):
    try:
        results = music_tool.get_suggest_keywords(
            keyword=data['keyword']
        )
        return results
    except Exception as e:
        return {"error": str(e)}

@router.post('/hi_res')
async def get_hi_res(data: dict = Body(...)):
    try:
        results = music_tool.get_hi_res(
            resource_types=data['resourceTypes'],
            with_url=data.get('withUrl', True),
            bitrate=data.get('bitrate', 320)
        )
        return [{
            'category': category.category,
            'songDetailVoList': [{
                'id': song.id,
                'name': song.name,
                'duration': song.duration,
                'albumName': song.album_name,
                'albumId': song.album_id,
                'albumArtistId': song.album_artist_id,
                'albumArtistName': song.album_artist_name,
                'artistId': song.artist_id,
                'artistName': song.artist_name,
                'coverImgUrl': song.cover_img_url,
                'mvId': song.mv_id,
                'playUrl': song.play_url,
                'playFlag': song.play_flag,
                'downloadFlag': song.download_flag,
                'payPlayFlag': song.pay_play_flag,
                'payDownloadFlag': song.pay_download_flag,
                'vipFlag': song.vip_flag,
                'vipPlayFlag': song.vip_play_flag,
                'liked': song.liked,
                'maxBrLevel': song.max_br_level,
                'plLevel': song.pl_level,
                'dlLevel': song.dl_level,
                'songSize': song.song_size,
                'songMd5': song.song_md5,
                'songTag': song.song_tag,
                'songFee': song.song_fee,
                'br': song.br,
                'audioFlag': song.audio_flag,
                'effects': song.effects,
                'visible': song.visible
            } for song in (category.song_detail_vo_list or [])],
            'playlistDetailVoList': [{
                'id': pl.id,
                'name': pl.name,
                'describe': pl.describe,
                'coverImgUrl': pl.cover_img_url,
                'creatorNickName': pl.creator_nick_name,
                'playCount': pl.play_count,
                'subscribedCount': pl.subscribed_count,
                'tags': pl.tags,
                'creatorId': pl.creator_id,
                'createTime': pl.create_time,
                'subed': pl.subed,
                'trackCount': pl.track_count,
                'specialType': pl.special_type
            } for pl in (category.playlist_detail_vo_list or [])],
            'albumDetailVoList': [{
                'id': album.id,
                'name': album.name,
                'language': album.language,
                'coverImgUrl': album.cover_img_url,
                'company': album.company,
                'transName': album.trans_name,
                'aliaName': album.alia_name,
                'genre': album.genre,
                'artists': [{
                    'id': artist.id,
                    'name': artist.name
                } for artist in album.artists],
                'briefDesc': album.brief_desc,
                'description': album.description,
                'publishTime': album.publish_time
            } for album in (category.album_detail_vo_list or [])]
        } for category in results]
    except Exception as e:
        return {"error": str(e)}

@router.post('/song_detail')
async def get_song_detail(data: dict = Body(...)):
    try:
        song_id = data.get('songId')
        with_url = data.get('withUrl', True)
        bitrate = data.get('bitrate')
        effects = data.get('effects')
        quality_flag = data.get('qualityFlag', False)
        language_flag = data.get('languageFlag', False)
        if not song_id:
            return {"error": "songId is required"}
        song = music_tool.get_song_detail(
            song_id=song_id,
            with_url=with_url,
            bitrate=bitrate,
            effects=effects,
            quality_flag=quality_flag,
            language_flag=language_flag
        )
        return {"song": song.__dict__}
    except Exception as e:
        return {"error": str(e)}

@router.post('/playlist/songs')
async def get_playlist_songs(data: dict = Body(...)):
    try:
        playlist_id = data.get('playlistId')
        limit = data.get('limit', 5)
        offset = data.get('offset', 0)
        if not playlist_id:
            return {"error": "playlistId is required"}
        songs = music_tool.get_playlist_songs(
            playlist_id=playlist_id,
            limit=limit,
            offset=offset
        )
        return [{
            'id': song.id,
            'name': song.name,
            'duration': song.duration,
            'albumName': song.album_name,
            'albumId': song.album_id,
            'albumArtistId': song.album_artist_id,
            'albumArtistName': song.album_artist_name,
            'artistId': song.artist_id,
            'artistName': song.artist_name,
            'coverImgUrl': song.cover_img_url,
            'mvId': song.mv_id,
            'playUrl': song.play_url,
            'playFlag': song.play_flag,
            'downloadFlag': song.download_flag,
            'payPlayFlag': song.pay_play_flag,
            'payDownloadFlag': song.pay_download_flag,
            'vipFlag': song.vip_flag,
            'vipPlayFlag': song.vip_play_flag,
            'liked': song.liked,
            'maxBrLevel': song.max_br_level,
            'plLevel': song.pl_level,
            'dlLevel': song.dl_level,
            'songSize': song.song_size,
            'songMd5': song.song_md5,
            'songTag': song.song_tag,
            'songFee': song.song_fee,
            'br': song.br,
            'audioFlag': song.audio_flag,
            'effects': song.effects,
            'visible': song.visible
        } for song in songs]
    except Exception as e:
        return {"error": str(e)}

@router.post('/album/free_songs')
async def get_album_free_songs(data: dict = Body(...)):
    try:
        album_id = data.get('albumId')
        limit = data.get('limit', 5)
        offset = data.get('offset', 0)
        if not album_id:
            return {"error": "albumId is required"}
        songs = music_tool.get_album_free_songs(
            album_id=album_id,
            limit=limit,
            offset=offset
        )
        return [{
            'id': song.id,
            'name': song.name,
            'duration': song.duration,
            'albumName': song.album_name,
            'albumId': song.album_id,
            'albumArtistId': song.album_artist_id,
            'albumArtistName': song.album_artist_name,
            'artistId': song.artist_id,
            'artistName': song.artist_name,
            'coverImgUrl': song.cover_img_url,
            'mvId': song.mv_id,
            'playUrl': song.play_url,
            'playFlag': song.play_flag,
            'downloadFlag': song.download_flag,
            'payPlayFlag': song.pay_play_flag,
            'payDownloadFlag': song.pay_download_flag,
            'vipFlag': song.vip_flag,
            'vipPlayFlag': song.vip_play_flag,
            'liked': song.liked,
            'maxBrLevel': song.max_br_level,
            'plLevel': song.pl_level,
            'dlLevel': song.dl_level,
            'songSize': song.song_size,
            'songMd5': song.song_md5,
            'songTag': song.song_tag,
            'songFee': song.song_fee,
            'br': song.br,
            'audioFlag': song.audio_flag,
            'effects': song.effects,
            'visible': song.visible
        } for song in songs]
    except Exception as e:
        return {"error": str(e)}

@router.post('/album/vip_songs')
async def get_album_vip_songs(data: dict = Body(...)):
    try:
        album_id = data.get('albumId')
        limit = data.get('limit', 5)
        offset = data.get('offset', 0)
        if not album_id:
            return {"error": "albumId is required"}
        songs = music_tool.get_album_vip_songs(
            album_id=album_id,
            limit=limit,
            offset=offset
        )
        return [{
            'id': song.id,
            'name': song.name,
            'duration': song.duration,
            'albumName': song.album_name,
            'albumId': song.album_id,
            'albumArtistId': song.album_artist_id,
            'albumArtistName': song.album_artist_name,
            'artistId': song.artist_id,
            'artistName': song.artist_name,
            'coverImgUrl': song.cover_img_url,
            'mvId': song.mv_id,
            'playUrl': song.play_url,
            'playFlag': song.play_flag,
            'downloadFlag': song.download_flag,
            'payPlayFlag': song.pay_play_flag,
            'payDownloadFlag': song.pay_download_flag,
            'vipFlag': song.vip_flag,
            'vipPlayFlag': song.vip_play_flag,
            'liked': song.liked,
            'maxBrLevel': song.max_br_level,
            'plLevel': song.pl_level,
            'dlLevel': song.dl_level,
            'songSize': song.song_size,
            'songMd5': song.song_md5,
            'songTag': song.song_tag,
            'songFee': song.song_fee,
            'br': song.br,
            'audioFlag': song.audio_flag,
            'effects': song.effects,
            'visible': song.visible
        } for song in songs]
    except Exception as e:
        return {"error": str(e)}

@router.post('/batch/song/urls')
async def get_batch_song_urls(data: dict = Body(...)):
    try:
        song_ids = data.get('songIds', [])
        bitrate = data.get('bitrate')
        effects = data.get('effects')
        if not song_ids:
            return {"error": "songIds is required"}
        urls = music_tool.get_batch_song_urls(
            song_ids=song_ids,
            bitrate=bitrate,
            effects=effects
        )
        return urls
    except Exception as e:
        return {"error": str(e)}

@router.post('/song/like')
async def like_song(data: dict = Body(...)):
    try:
        song_id = data.get('songId')
        is_like = data.get('isLike', True)
        if not song_id:
            return {"error": "songId is required"}
        result = music_tool.like_song(
            song_id=song_id,
            is_like=is_like
        )
        return {
            "success": True,
            "message": "Successfully " + ("liked" if is_like else "unliked") + " the song",
            "data": result
        }
    except Exception as e:
        return {"error": str(e)}

@router.post('/test_agent')
async def test_music_agent_route(data: dict = Body(...)):
    try:
        prompt = data.get('prompt')
        if not prompt:
            return {"error": "prompt is required"}
        from tools.NetEaseCloudMusic.music_autogen_agent import test_music_agent
        import asyncio
        result = await test_music_agent(prompt)
        return result
    except Exception as e:
        return {"error": str(e)}


if __name__ == '__main__':
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='启动 Web API 服务')
    parser.add_argument('--port', type=int, default=5000, help='服务运行的端口号 (默认: 5000)')
    parser.add_argument('--host', type=str, default='0.0.0.0', help='服务监听的主机地址 (默认: 0.0.0.0)')
    parser.add_argument('--debug', action='store_true', help='是否开启调试模式 (默认: False)')
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 运行服务
    # app.run(
    #     host=args.host,
    #     port=args.port,
    #     debug=args.debug
    # )
