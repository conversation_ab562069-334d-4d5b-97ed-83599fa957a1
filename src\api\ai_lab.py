import uuid
from fastapi import APIRouter, HTTPException, Request, Query
from pydantic import BaseModel
from typing import List, Optional
# 需要补充的依赖导入
from src.database.mysql_client import get_db  # 如路径不同请调整
from sqlalchemy.future import select
from sqlalchemy.orm import selectinload
from sqlalchemy import func, update, Boolean
from datetime import datetime
from src.models.ailab.ailab_schemas import UserVpaInstance, GenerationSourceEnum, VpaActionPrompt, VpaSeqLog, UserVpaGifInstance, VpaInstanceStatus
from src.tools.vpa.vpa import _generate_text_to_img, _generate_img_to_img, _generate_img_to_gif, \
    _handle_individual_param_callback, _call_model_api, logger

router = APIRouter(prefix="/api/ai_lab", tags=["AI LAB 服务"])

class TextToImgRequest(BaseModel):
    prompt: str
    user_id: int
    style:str
    is_refine: bool = False
    nickname: Optional[str] = None
    base_persona_id: Optional[int] = None
    generation_style_id: Optional[int] = None

class TextToImgResponse(BaseModel):
    ids: List[int]
    task_id: int

class StyleDesc:
    STATUS_DESCRIPTION = {
        "卡通": "卡通风格，简单背景，正面，全身照，双脚站立，双手自然垂放。",
        "动漫": "动漫风格，简单背景，正面，全身照，双脚站立，双手自然垂放。"
    }
    @classmethod
    def get_style_description(cls, style: str) -> str:
        """
        获取指定风格的描述文本
        :param style: 风格名称（如'卡通'）
        :return: 对应的描述文本，若风格不存在则返回'未知风格'
        """
        return cls.STATUS_DESCRIPTION.get(style, "未知风格")

class VpaLogResponse(BaseModel):
    id: int
    instance_id: int
    task_id: int
    seq_id: str
    result_url: str
    status_id: int
    created_at: datetime
    updated_at: datetime

@router.post("/text_to_img", response_model=TextToImgResponse, summary="基础形象生成")
async def text_to_img_api(
    req: TextToImgRequest
):
    if req.is_refine:
        prompt = req.prompt
    else:
        desc = StyleDesc.get_style_description(req.style)
        prompt= f"{req.prompt}，{desc}"
    async with get_db() as db:
        task_results = []
        task_id = None
        now = datetime.now()
        vpa = UserVpaInstance(
            user_id=req.user_id,
            nickname=req.nickname or prompt,
            status_id=VpaInstanceStatus.GENERATING,  # 制作中
            is_currently_active=False,
            generation_prompt=prompt,
            created_at=now,
            updated_at=now
        )
        db.add(vpa)
        await db.flush()  # 获取自增ID
        # 新增日志
        for i in range(4):
            result = await _generate_text_to_img(prompt)
            vpa_log = VpaSeqLog(
                instance_id=vpa.id,
                task_id=result.get("task_id"),
                seq_id=result.get("seq_id"),
                status_id=VpaInstanceStatus.GENERATING,
                created_at=now,
                updated_at=now
            )
            db.add(vpa_log)
            await db.flush()  # 立即刷新到数据库生成ID
            task_results.append(vpa_log.id)  # 此时已能获取真实ID
            if not task_id:
                task_id = result.get("task_id") or result.get("seq_id")
        await db.commit()  # 最终提交事务
        return TextToImgResponse(ids=task_results, task_id=task_id)

class GenerateAvatarRequest(BaseModel):
    user_id: int
    vpa_id: int  # 基础形象ID
    action_ids: Optional[List[int]] = None  # 动作配置ID列表，非必填；如果action_ids未传，生成所有动作
    controller_type: str = "canny"  # canny/depth/pose
    strength: float = 0.6
    scale: float = 3.0
    duration: int = 5  # 动作持续时间，5或10秒

class GenerateAvatarResponse(BaseModel):
    task_ids: List[int]  # 生成任务ID列表
    gif_instance_ids: List[int]  # 动作GIF实例ID列表

@router.post("/generate_avatar", response_model=GenerateAvatarResponse, summary="动作形象生成")
async def generate_avatar(
    req: GenerateAvatarRequest
):
    """
    数字人生成接口（仅到图生图任务日志保存，后续动作生成独立接口）
    """
    async with get_db() as db:
        base_vpa = await db.get(UserVpaInstance, req.vpa_id)
        if not base_vpa:
            raise HTTPException(status_code=404, detail=f"基础形象不存在: {req.vpa_id}")
        # 如果action_ids未传，自动查所有动作id
        action_ids = req.action_ids
        if not action_ids:
            stmt = select(VpaActionPrompt)
            action_list = (await db.execute(stmt)).scalars().all()
            action_ids = [a.id for a in action_list]
        task_ids = []
        img_instance_ids = []
        now = datetime.now()
        for action_id in action_ids:
            try:
                action_config = await db.get(VpaActionPrompt, action_id)
                if not action_config:
                    continue
                # 新增或更新 user_vpa_gif_instances
                gif_instance = await db.scalar(
                    select(UserVpaGifInstance)
                    .where(UserVpaGifInstance.vpa_id == base_vpa.id)
                    .where(UserVpaGifInstance.action_id == action_id)
                )
                if not gif_instance:
                    gif_instance = UserVpaGifInstance(
                        vpa_id=base_vpa.id,
                        action_id=action_id,
                        status_id=VpaInstanceStatus.GENERATING,
                        is_currently_active=False,
                        created_at=now,
                        updated_at=now
                    )
                    db.add(gif_instance)
                    await db.flush()
                else:
                    gif_instance.status_id = VpaInstanceStatus.GENERATING
                    gif_instance.updated_at = now
                img_instance_ids.append(gif_instance.id)
                img_result = await _generate_img_to_img(
                    prompt=action_config.description,
                    image=base_vpa.thumbnail_url
                )
                img_task_id = img_result.get("task_id")
                task_ids.append(img_task_id)
                img_task_log = VpaSeqLog(
                    instance_id=gif_instance.id,
                    task_id=img_result.get("task_id"),
                    seq_id=img_result.get("seq_id"),
                    status_id=VpaInstanceStatus.GENERATING,  # 制作中
                    created_at=now,
                    updated_at=now
                )
                db.add(img_task_log)
            except Exception as e:
                import logging
                logging.error(f"generate_avatar action_id={action_id} failed: {e}")
                img_task_log = VpaSeqLog(
                    instance_id=gif_instance.id,
                    task_id=img_result.get("task_id"),
                    seq_id=img_result.get("seq_id"),
                    status_id=VpaInstanceStatus.FAILED,  # 制作中
                    created_at=now,
                    updated_at=now
                )
                db.add(img_task_log)
                continue
        await db.commit()
        return GenerateAvatarResponse(task_ids=task_ids, gif_instance_ids=img_instance_ids)

class GenerateAvatarActionRequest(BaseModel):
    vpa_id: int
    action_id: int
    img_url: Optional[str] = None  # 图生图生成的图片URL
    duration: int = 5

class GenerateAvatarActionResponse(BaseModel):
    gif_task_id: int
    gif_instance_id: int

@router.post("/generate_avatar_action", response_model=GenerateAvatarActionResponse, summary="动作生成")
async def generate_avatar_action(req: GenerateAvatarActionRequest):
    """
    数字人动作生成接口（图生视频及GIF实例管理）
    """
    async with get_db() as db:
        base_vpa = await db.get(UserVpaInstance, req.vpa_id)
        if not base_vpa:
            raise HTTPException(status_code=404, detail=f"基础形象不存在: {req.vpa_id}")
        action_config = await db.get(VpaActionPrompt, req.action_id)
        if not action_config:
            raise HTTPException(status_code=404, detail=f"动作配置不存在: {req.action_id}")
        img_url = req.img_url or base_vpa.thumbnail_url
        gif_result = await _generate_img_to_gif(
            prompt=action_config.action_prompt,
            image=img_url
        )
        now = datetime.now()
        gif_instance = await db.scalar(
            select(UserVpaGifInstance)
            .where(UserVpaGifInstance.vpa_id == req.vpa_id)
            .where(UserVpaGifInstance.action_id == req.action_id)
        )
        if not gif_instance:
            gif_instance = UserVpaGifInstance(
                vpa_id=req.vpa_id,
                action_id=req.action_id,
                status_id=VpaInstanceStatus.GENERATING,
                is_currently_active=False,
                thumbnail_url = img_url,
                created_at=now,
                updated_at=now
            )
            db.add(gif_instance)
            await db.flush()
        else:
            gif_instance.status_id = VpaInstanceStatus.GENERATING
            gif_instance.thumbnail_url = img_url
            gif_instance.updated_at = now
        # 新增日志
        gif_task_log = VpaSeqLog(
            instance_id=gif_instance.id,
            task_id=gif_result.get("task_id"),
            seq_id=gif_result.get("seq_id"),
            status_id=VpaInstanceStatus.GENERATING,
            created_at=now,
            updated_at=now
        )
        db.add(gif_task_log)
        await db.commit()
        return GenerateAvatarActionResponse(gif_task_id=gif_result.get("task_id"), gif_instance_id=gif_instance.id)

@router.post("/callback", summary="VPA回调接口")
async def receive_callback(request: Request):
    payload = await request.json()
    result = await _handle_individual_param_callback(payload)
    task_id = payload.get("task_id")
    seq_id = payload.get("seq_id")
    flag = payload.get("flag")
    status_id = VpaInstanceStatus.ACTIVE if flag == 1 else VpaInstanceStatus.FAILED
    now = datetime.now()
    async with get_db() as db:
        #首先更新日志
        log = None
        if seq_id:
            log = await db.scalar(select(VpaSeqLog).where(VpaSeqLog.seq_id == seq_id))
            log.status_id = status_id
            log.updated_at = now
        #获取结果
        url = None
        data = payload.get("data")
        if isinstance(data, dict):
            task_result = data.get("task_result")
            if isinstance(task_result, dict):
                if task_id == 30009:
                    out_video = task_result.get("out_video")
                    if isinstance(out_video, list) and out_video:
                        url = out_video[0]
                else:
                    out_image = task_result.get("out_image")
                    if isinstance(out_image, list) and out_image:
                        url = out_image[0]
        if url:
            log.result_url = url
            if task_id == 10011:
                if log:
                    pass
                    # vpa_instance = await db.get(UserVpaInstance, log.instance_id)
                    # if vpa_instance:
                    #     vpa_instance.status_id = status_id
                    #     vpa_instance.thumbnail_url = url
                    #     vpa_instance.updated_at = now
                    # await db.commit()
            elif task_id == 30009:
                if log:
                    gif_instance = await db.get(UserVpaGifInstance, log.instance_id)
                    if gif_instance:
                        gif_instance.status_id = status_id
                        gif_instance.gif_url = url
                        gif_instance.updated_at = now
                    await db.commit()
            elif task_id == 20031:
                gif_instance = await db.get(UserVpaGifInstance, log.instance_id)
                if gif_instance:
                    vpa_id = gif_instance.vpa_id
                    action_id = gif_instance.action_id
                    req_obj = GenerateAvatarActionRequest(
                        vpa_id=vpa_id,
                        action_id=action_id,
                        img_url=url,
                        duration=payload.get("duration", 5)
                    )
                    await generate_avatar_action(req_obj)
        await db.commit()
    return result

@router.get("/vpa_list", summary="VPA列表查询（基础形象信息），包含公共形象（user_id=0）")
async def get_vpa_list(user_id: int):
    async with get_db() as db:
        stmt = select(UserVpaInstance)
        stmt = stmt.where((UserVpaInstance.user_id == user_id) | (UserVpaInstance.user_id == 0))
        stmt = stmt.order_by(UserVpaInstance.user_id.asc(), UserVpaInstance.id.desc())
        vpa_list = (await db.execute(stmt)).scalars().all()
        return vpa_list

@router.get("/vpa_page", summary="VPA分页查询（基础形象信息），包含公共形象（user_id=0）")
async def get_vpa_page(user_id: int, page: int = 1, page_size: int = 10):
    async with get_db() as db:
        stmt = select(UserVpaInstance)
        stmt = stmt.where((UserVpaInstance.user_id == user_id) | (UserVpaInstance.user_id == 0))
        stmt = stmt.order_by(UserVpaInstance.user_id.asc(), UserVpaInstance.id.desc())
        # 统计总数
        count_stmt = select(func.count()).select_from(UserVpaInstance)
        count_stmt = count_stmt.where((UserVpaInstance.user_id == user_id) | (UserVpaInstance.user_id == 0))
        total = (await db.execute(count_stmt)).scalar()
        offset = (page - 1) * page_size
        stmt = stmt.offset(offset).limit(page_size)
        vpa_list = (await db.execute(stmt)).scalars().all()
        return {
            "total": total,
            "page": page,
            "page_size": page_size,
            "items": vpa_list
        }

@router.get("/vpa_detail", summary="VPA详情查询（基础形象及动作形象、动作信息）")
async def get_vpa_detail(vpa_id: int):
    async with get_db() as db:
        vpa = await db.get(UserVpaInstance, vpa_id)
        # 查询gif实例及动作信息
        gif_instances = []
        gif_query = select(UserVpaGifInstance).where(UserVpaGifInstance.vpa_id == vpa.id)
        gif_list = (await db.execute(gif_query)).scalars().all()
        for gif in gif_list:
            action_prompt = None
            action_type = None
            if gif.action_id:
                action = await db.get(VpaActionPrompt, gif.action_id)
                if action:
                    action_prompt = getattr(action, 'action_prompt', None)
                    action_type = getattr(action, 'action_type', None)
            gif_instances.append({
                "id": gif.id,
                "action_id": gif.action_id,
                "status_id": gif.status_id,
                "is_currently_active": gif.is_currently_active,
                "thumbnail_url": getattr(gif, 'thumbnail_url', None),
                "gif_url": getattr(gif, 'gif_url', None),
                "created_at": gif.created_at,
                "updated_at": gif.updated_at,
                "action_prompt": action_prompt,
                "action_type": action_type
            })
        # 查询日志
        # 1. 形象日志（task_id=10011）
        logs_vpa = (await db.execute(
            select(VpaSeqLog).where(
                VpaSeqLog.instance_id == vpa.id,
                VpaSeqLog.task_id == 10011
            )
        )).scalars().all()
        log_records = [{
            "id": log.id,
            "instance_id": log.instance_id,
            "task_id": getattr(log, 'task_id', None),
            "seq_id": getattr(log, 'seq_id', None),
            "status_id": log.status_id,
            "created_at": log.created_at,
            "updated_at": log.updated_at
        } for log in logs_vpa]
        # 2. gif日志（task_id=20031或30009）
        for gif in gif_list:
            logs_gif = (await db.execute(
                select(VpaSeqLog).where(
                    VpaSeqLog.instance_id == gif.id,
                    VpaSeqLog.task_id.in_([20031, 30009])
                )
            )).scalars().all()
            for log in logs_gif:
                log_records.append({
                    "id": log.id,
                    "instance_id": log.instance_id,
                    "task_id": getattr(log, 'task_id', None),
                    "seq_id": getattr(log, 'seq_id', None),
                    "status_id": log.status_id,
                    "created_at": log.created_at,
                    "updated_at": log.updated_at
                })
        return {
            "id": vpa.id,
            "user_id": vpa.user_id,
            "nickname": getattr(vpa, 'nickname', None),
            "status_id": vpa.status_id,
            "is_currently_active": vpa.is_currently_active,
            "generation_prompt": getattr(vpa, 'generation_prompt', None),
            "thumbnail_url": getattr(vpa, 'thumbnail_url', None),
            "created_at": vpa.created_at,
            "updated_at": vpa.updated_at,
            "gif_instances": gif_instances,
            "logs": log_records
        }

@router.get("/action_prompt_list", summary="可用的动作列表查询")
async def get_action_prompt_list():
    async with get_db() as db:
        stmt = select(VpaActionPrompt)
        action_list = (await db.execute(stmt)).scalars().all()
        result = []
        for action in action_list:
            result.append({
                "id": action.id,
                "action_type": getattr(action, 'action_type', None),
                "action_prompt": getattr(action, 'action_prompt', None),
                "description": getattr(action, 'description', None),
                "created_at": getattr(action, 'created_at', None),
                "updated_at": getattr(action, 'updated_at', None)
            })
        return result


from fastapi import HTTPException


@router.get("/get_log_urls", response_model=List[VpaLogResponse])
async def get_log_urls(vpa_id: int = Query(...)):  # 直接接收列表，不需要外层包装
    logs = []
    try:
        # 执行查询
        async with get_db() as db:
            stmt = select(VpaSeqLog).where(VpaSeqLog.instance_id == vpa_id)
            result = await db.execute(stmt)
            logs = result.scalars().all()


        return [VpaLogResponse(
                id=log.id,
                instance_id=log.instance_id,
                task_id=log.task_id,
                seq_id=log.seq_id,
                result_url=log.result_url,
                status_id=log.status_id,
                created_at=log.created_at,
                updated_at=log.updated_at
            ) for log in logs]

    except ValueError as e:
        raise HTTPException(400, detail=str(e))
    except Exception as e:
        logger.error(f"数据库查询失败: {e}", exc_info=True)
        raise HTTPException(500, detail="服务器内部错误")


@router.post("/update_vpa_instance")
async def update_vpa_instance(request: Request):  # 让FastAPI自动解析JSON
    try:
        data = await request.json()
        vpalog_id = data.get("vpalog_id")
        if not vpalog_id:
            raise HTTPException(422, "Missing vpalog_id in JSON")
    except Exception as e:
        raise HTTPException(500, str(e))

    async with get_db() as db:
        try:
            # 1. 从VpaSeqLog获取源数据
            stmt = select(
                VpaSeqLog.instance_id,
                VpaSeqLog.result_url,
                VpaSeqLog.seq_id
            ).where(VpaSeqLog.id == vpalog_id)

            print("执行的SQL:", stmt.compile(compile_kwargs={"literal_binds": True}))
            log_data = await db.execute(stmt)
            print('log_data:', log_data)
            row = log_data.first()
            print('row:', row)

            if not row:
                print(f"未找到ID为 {vpalog_id} 的VpaSeqLog记录")
                raise HTTPException(404, "指定日志记录不存在")

            instance_id, result_url, seq_id = row
            print(f"查询结果 - instance_id: {instance_id}, result_url: {result_url}, seq_id: {seq_id}")
            if not result_url:
                print("结果URL为空")
                raise HTTPException(404, "结果URL为空")

            # 2. 更新UserVpaInstance表
            if not instance_id:
                print("instance_id为空，无法更新UserVpaInstance")
                raise HTTPException(400, "instance_id不能为空")

            update_stmt = (
                update(UserVpaInstance)
                .where(UserVpaInstance.id == instance_id)
                .values(
                    asset_id=seq_id,
                    thumbnail_url=result_url,
                    updated_at=datetime.now(),
                    status_id=VpaInstanceStatus.ACTIVE
                )
            )
            print("更新SQL:", update_stmt.compile(compile_kwargs={"literal_binds": True}))
            result = await db.execute(update_stmt)

            if result.rowcount == 0:
                print(f"未找到ID为 {instance_id} 的UserVpaInstance记录")
                raise HTTPException(404, "指定的用户实例不存在")

            await db.commit()

            return {
                "success": True,
                "message": "更新成功",
                "data": {
                    "instance_id": instance_id,
                    "asset_id": seq_id,
                    "thumbnail_url": result_url
                }
            }

        except HTTPException:
            await db.rollback()
            raise
        except Exception as e:
            await db.rollback()
            print(f"更新过程中发生错误: {str(e)}")
            raise HTTPException(500, f"更新失败: {str(e)}")


@router.post("/prompt_refine", summary="通用模型API调用")
async def prompt_refine(request: Request):
    try:
        # 获取请求的 JSON 数据
        raw_data = await request.json()
        print("Received data:", raw_data)  # 调试输出，打印接收到的原始数据

        # 检查请求数据中是否包含 "user_input" 字段
        if "user_input" not in raw_data:
            raise HTTPException(
                status_code=400,
                detail="请求数据中缺少 'user_input' 字段"
            )

        # 获取并验证 "user_input" 字段的内容
        user_input = raw_data["user_input"]
        if not user_input or len(user_input.strip()) == 0:
            raise HTTPException(
                status_code=400,
                detail="'user_input' 字段不能为空"
            )

        # 调用模型API，并传入有效的 user_input
        content = await _call_model_api(user_input)

        # 返回API调用的结果
        return {"result": content}

    except HTTPException as http_exc:
        # 捕获 HTTPException 类型的异常并返回相应的错误信息
        raise http_exc

    except Exception as e:
        # 捕获所有其他异常，返回500错误并附带详细的异常信息
        print(f"发生错误: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"处理请求时发生错误: {e}"
        )

