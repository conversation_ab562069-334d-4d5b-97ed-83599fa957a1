import logging
from fastapi import UploadFile, HTTPException
from typing import List
from src.tools.comfyui.comfyui import load_workflow, upload_image, send_prompt, wait_for_video
import json
import traceback
import uuid
from src.database.minio_client import MinioClient

logger = logging.getLogger("image_to_video")
server_name = ""
minio_client = MinioClient()

async def download_and_upload_to_minio(video_url: str, object_prefix: str = "video") -> str:
    import httpx
    async with httpx.AsyncClient() as client:
        resp = await client.get(video_url)
        resp.raise_for_status()
        content = resp.content
        object_name = f"{object_prefix}/{uuid.uuid4()}.mp4"
        minio_url = await minio_client.upload_file_content(content, object_name, content_type="video/mp4")
        return minio_url

def build_image_to_video_workflow(image_path: str):
    """构建图片生成视频工作流"""
    try:
        workflow = load_workflow("image_to_video.json")
        if "11" not in workflow:
            logger.error("工作流中找不到节点11")
            raise HTTPException(status_code=500, detail="Node 11 not found in workflow")
        workflow["11"]["inputs"]["image"] = image_path
        logger.info(f"设置图片: {image_path}")
        if "20" not in workflow:
            logger.error("工作流中找不到节点20")
            raise HTTPException(status_code=500, detail="Node 20 not found in workflow")
        return workflow, "20"
    except Exception as e:
        logger.error(f"构建工作流时出错: {str(e)}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Error building workflow: {str(e)}")

async def image_to_video_comfyui(file: UploadFile):
    try:
        logger.info(f"\n========== 开始图片生成视频 ==========")
        logger.info(f"文件名: {file.filename}")
        uploaded_filename = await upload_image(server_name, file.file, "待生成视频图片")
        logger.info(f"成功上传文件，获得文件名称: {uploaded_filename}")
        workflow, output_node_id = build_image_to_video_workflow(uploaded_filename)
        logger.info(f"\n当前工作流配置: {json.dumps(workflow, indent=2)}")
        data = await send_prompt(server_name, workflow)
        logger.info(f"ComfyUI API 响应: {data}")
        # 等待视频生成，获取视频文件路径
        video_path = await wait_for_video(server_name, data["prompt_id"], output_node_id, "")
        logger.info(f"生成的视频路径: {video_path}")
        minio_url = await download_and_upload_to_minio(video_path)
        return minio_url
    except HTTPException as e:
        logger.error(f"HTTP异常: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"调用image_to_video接口出错: {str(e)}")
        raise HTTPException(status_code=500, detail="内部服务器错误")

async def image_to_video(files: List[UploadFile]) -> List[str]:
    if not files:
        return []
    videos = []
    for idx, file in enumerate(files):
        if not file.filename or (hasattr(file, 'size') and file.size == 0):
            continue
        video_url = await image_to_video_comfyui(file)
        videos.append(video_url)
    return videos 