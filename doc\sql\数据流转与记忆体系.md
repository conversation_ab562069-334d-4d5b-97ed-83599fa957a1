# AutoPilot AI - 数据流转与记忆体系

本文档详细描述了 AutoPilot AI 系统中，一次完整的用户交互（从请求发起到任务结束）所涉及的核心数据流转、**任务状态管理**以及与**分层记忆系统**的交互机制。该设计旨在确保高并发下的实时性能、数据一致性与长期记忆的有效沉淀。

## 核心原则

- **状态管理与记忆存储分离**：
  - **Redis**: 作为高性能的"任务状态管理器"，专门处理任务执行过程中的实时状态跟踪、进度管理和临时数据缓存
  - **记忆体系统**: 由MySQL、MongoDB、Neo4j构成的持久化记忆存储，负责长期记忆的沉淀、学习和智能关联
- **任务ID贯穿始终**：一个全局唯一的 `task_id` 是串联状态管理系统和记忆系统的关键，确保了端到端的可追溯性。
- **异步记忆沉淀**：任务状态的实时管理与记忆的持久化完全解耦。记忆沉淀作为任务结束后的异步步骤，与用户响应解耦。

## 端到端数据流转图

### 主流程图

```mermaid
flowchart TD
    A[用户发起复杂任务<br/>如:规划行程] --> B[API网关接收请求<br/>POST /v1/tasks]
    
    B --> C{任务初始化<br/>三方占坑}
    C --> C1[生成唯一task_id]
    C1 --> C2[MySQL占坑<br/>INSERT ai_planning_sessions<br/>status: PROCESSING]
    C2 --> C3[MongoDB占坑<br/>INSERT ai_interaction_logs<br/>初始状态]
    C3 --> C4[Redis创建作战室<br/>HSET task:task_id<br/>设置TTL]
    
    C4 --> D[返回task_id给用户]
    D --> E[用户建立SSE连接<br/>GET /v1/tasks/task_id/stream]
    
    E --> F{任务执行阶段<br/>实时读写Redis}
    F --> F1[启动AI引擎]
    F1 --> F2[记忆检索Agent]
    F2 --> F2a[MySQL: 获取用户画像和偏好]
    F2 --> F2b[MongoDB: 获取历史交互记录]
    F2 --> F2c[Neo4j: 获取语义关联]
    F2a --> F3[加载记忆到Redis任务状态]
    F2b --> F3
    F2c --> F3
    
    F3 --> F4[规划Agent开始工作<br/>实时更新Redis状态]
    F4 --> F5{是否需要交互确认?}
    
    F5 -->|是| G[暂停执行<br/>推送选项给用户]
    G --> G1[用户做出选择]
    G1 --> G2[POST /v1/tasks/task_id/continue<br/>更新Redis状态]
    G2 --> F4
    
    F5 -->|否| H[任务完成<br/>report_success]
    
    H --> I{数据归档阶段<br/>Archiving}
    I --> I1[HGETALL从Redis获取<br/>完整执行记录]
    I1 --> I2[updateOne MongoDB<br/>填充完整日志]
    I2 --> I3[UPDATE MySQL<br/>更新任务状态为SUCCESS]
    I3 --> I4[DEL Redis<br/>清理任务Hash]
    
    I4 --> J{记忆沉淀阶段<br/>Learning}
    J --> J1[调用记忆生成/评估Agent]
    J1 --> J2[MySQL: 更新用户画像和偏好]
    J1 --> J3[MongoDB: 存储完整交互记录]
    J1 --> J4[Neo4j: 构建三元组知识网络]
    
    J2 --> K[推送最终结果给用户<br/>关闭SSE连接]
    J3 --> K
    J4 --> K
    
    style A fill:#e1f5fe
    style C fill:#fff3e0
    style F fill:#f3e5f5
    style I fill:#e8f5e8
    style J fill:#fce4ec
    style K fill:#e0f2f1
```

### 异常处理流程图

```mermaid
flowchart TD
    A1[引擎上报失败] --> D[异常处理流程]
    A2[用户主动取消API] --> D
    A3[任务超时] --> D
    
    D --> E[1. 通知用户<br/>SSE event:task_failed]
    E --> F[2. 归档异常信息<br/>Redis->MongoDB/MySQL]
    F --> G[3. 清理Redis资源<br/>DEL task:task_id]
    
    style A1 fill:#ffebee
    style A2 fill:#ffebee
    style A3 fill:#ffebee
    style D fill:#fff3e0
    style E fill:#e3f2fd
    style F fill:#e8f5e8
    style G fill:#f1f8e9
```

### 系统架构组件图

```mermaid
graph LR
    subgraph "用户端"
        U[车机/手机]
    end
    
    subgraph "API层"
        G[API网关/中枢]
    end
    
    subgraph "存储层"
        R[Redis<br/>任务状态管理]
        M[MongoDB<br/>详细日志存储]
        S[MySQL<br/>结构化记忆]
        N[Neo4j<br/>关系网络]
    end
    
    subgraph "AI引擎"
        E[规划Agent]
        MA[记忆检索Agent]
        MG[记忆生成Agent]
    end
    
    U <--> G
    G <--> R
    G <--> M
    G <--> S
    G <--> N
    G <--> E
    E <--> MA
    E <--> MG
    MA <--> R
    MA <--> M
    MA <--> S
    MA <--> N
    MG <--> M
    MG <--> S
    MG <--> N
    
    style U fill:#e1f5fe
    style G fill:#fff3e0
    style R fill:#ffebee
    style M fill:#e8f5e8
    style S fill:#f3e5f5
    style N fill:#fce4ec
    style E fill:#e0f2f1
    style MA fill:#f1f8e9
    style MG fill:#e3f2fd
```

## 数据流转详解

### 阶段一：任务初始化 (三方占坑)

当用户发起一个需要 AutoPilot AI 处理的复杂任务时，系统首先确保该任务的"身份"被牢固确立，即使后续处理失败，也能追踪到记录。

1.  **生成唯一ID**: API网关或中枢系统为本次交互生成一个全局唯一的 `task_id` (例如: `UUID`)。
2.  **MySQL 占坑**: 在 `ai_planning_sessions` 表中插入一条新记录。此记录的核心是 `task_id` 和一个初始状态，如 `'PROCESSING'`。这提供了一个可靠的、事务性的任务总览。
3.  **MongoDB 占坑**: 在 `ai_interaction_logs` 集合中插入一个"骨架"文档。该文档仅包含 `task_id`、`user_id` 和初始状态，为未来的详细日志归档预留了位置。
4.  **Redis 创建"作战室"**: 在 Redis 中创建一个 **Hash**，其 `key` 为 `task:{task_id}`。这个 Hash 将作为本次任务执行期间所有动态数据的"实时白板"。同时，为其设置一个合理的过期时间（如30分钟），作为超时自动清理的保险措施。

完成这三步后，任务的持久化记录和实时处理空间均已备好。系统可以安全地向客户端返回 `task_id`，并开始真正的智能处理。

### 阶段二：任务执行 (实时读写 Redis)

此阶段是 AI 引擎的核心工作区域，所有操作都围绕高性能的 Redis 进行，以最大程度降低延迟，并向前端提供流畅的实时反馈。

1.  **记忆检索 (从记忆体系统加载到任务状态)**:
    -   **规划类Agent** (如 `TripPlanningAgent`) 启动后，首先会通过 **记忆检索Agent** 从记忆体系统中拉取相关记忆：
        - **MySQL**: 获取用户画像、核心偏好等结构化记忆
        - **MongoDB**: 获取历史交互记录、行为模式等非结构化记忆
        - **Neo4j**: 获取关系网络、语义关联等图谱记忆
    -   这些检索到的记忆被临时加载到当前任务在 Redis 的 Hash 中，作为任务执行期间的上下文数据。

2.  **实时状态更新**:
    -   AI引擎中的各个Agent（规划、搜索、分析等）在执行过程中，会产生大量的中间状态和日志。
    -   这些信息，包括对业务有意义的步骤 (`business_steps_log`)、模型的原始思考链 (`raw_model_trace`)、当前的进度百分比等，都会被实时地 `HSET` 到 Redis 的任务Hash中。
    -   这些数据仅用于任务状态跟踪和实时反馈，生命周期仅限于当前任务执行期间。

3.  **智能决策与交互确认 (可选)**:
    -   **智能决策**: Agent不仅仅是调用工具，更是在执行过程中进行复杂的智能决策。例如，`TripPlanningAgent`会基于用户偏好、行程天数等因素，从大量候选中**精选**出少量核心景点，并**优化游览顺序**。这些决策的中间结果（如候选列表、推荐理由）都会被记录在Redis的Hash中。
    -   **交互确认**: 对于关键决策点（如最终去哪些景点），系统可以设计为暂停执行，并通过SSE向客户端推送一个`interactive_choice_required`事件，将选择权交给用户。
        -   此时，引擎暂停，等待用户通过一个专门的接口（如 `POST /v1/tasks/{task_id}/continue`）提交选择。
        -   网关接收到用户的选择后，更新Redis中的任务状态，并唤醒对应的Agent继续执行。
        -   这个交互环节是**可插拔**的，在不需要用户干预的场景下可以被跳过，实现全自动规划。

4.  **实时进度反馈**:
    -   一个独立的SSE（Server-Sent Events）服务负责监控 Redis 中任务 Hash 的变化。
    -   一旦检测到内容更新，它会立刻将最新的进度信息通过流式连接推送到车端，供UI展示。
    -   **此阶段完全不涉及对 MySQL 和 MongoDB 的任何读写**，确保了核心规划链路的最高性能。

### 阶段三：任务结束 (归档与记忆沉淀)

当 AI 引擎成功完成任务并生成最终结果后，系统进入收尾阶段，将"作战室"的成果进行永久封存和学习。

1.  **数据归档 (Archiving)**:
    -   **从Redis读取**: 任务协调器执行 `HGETALL`，从 Redis 中一次性取出任务 Hash 的所有数据，这包含了从开始到结束的完整执行记录。
    -   **更新MongoDB**: 执行一次 `updateOne` 操作，用从Redis获取到的完整数据填充在第一阶段创建的"骨架"文档。同时，将文档状态更新为 `'SUCCESS'` 或 `'FAILED'`。至此，一份详尽的、可供分析和调试的"黑匣子"日志已永久保存在 `ai_interaction_logs` 中。
    -   **更新MySQL**: 执行 `UPDATE` 操作，更新 `ai_planning_sessions` 表中的对应任务状态，完成任务状态的闭环。
    -   **清理Redis**: 执行 `DEL` 命令，删除对应的任务 Hash，释放内存资源。

2.  **记忆沉淀 (Learning)**:
    -   **记忆生成Agent** 会分析本次交互的完整日志（特别是用户的输入和最终选择）。
    -   如果识别出新的、有价值的用户偏好或事实，**记忆评估Agent** 会对其进行打分。
    -   对于高价值的记忆，系统会分层存储到记忆体系统：
        - **MySQL**: 存储结构化的用户画像更新和核心偏好记忆
        - **MongoDB**: 存储完整的交互记录和行为模式
        - **Neo4j**: 存储提取的三元组知识和关系网络
    -   这个三层记忆存储确保了从结构化精华到非结构化全量，再到关系网络的全面记忆覆盖。

这个闭环流程确保了系统在提供高性能实时体验的同时，也构建了一个能够持续学习和进化的、安全可靠的长期记忆系统。

---

## 异常处理机制

为保证系统的稳定性和用户体验，设计了多层级的异常处理机制：

### 1. 任务执行超时 (主动监控)
- **机制**: AutoPilot AI引擎内部会对每个任务启动一个独立的计时器（例如，总时长不超过5分钟）。
- **处理**: 如果任务执行时间超出阈值，引擎会主动中断当前所有操作，将任务标记为失败，并附上`'TIMEOUT'`的错误信息。随后，立即触发**数据归档**流程，将失败状态和日志持久化，并通知用户。

### 2. 工具调用失败与重试 (弹性容错)
- **机制**: 在调用外部API（如高德地图）时，如果遇到可恢复的错误（如网络波动、服务器临时5xx错误），系统不会立即失败。
- **处理**: 引擎会内置一套重试逻辑（例如，最多重试3次，采用指数退避策略增加重试间隔）。如果在所有重试后依然失败，则判定该步骤无法完成，整个任务失败。`tool_calls_log`中会详细记录每次的尝试与失败信息。

### 3. 用户主动取消
- **机制**: 系统需提供一个API端点（如 `DELETE /v1/tasks/{task_id}`），允许客户端主动请求取消一个正在进行中的任务。
- **处理**:
    1.  API网关接收到请求后，会向AI引擎发送一个"停止"信号。
    2.  引擎收到信号后，优雅地终止当前计算。
    3.  任务状态在Redis、MySQL、MongoDB中被统一更新为 `'CANCELED'`。
    4.  最后清理Redis资源，并关闭与客户端的SSE连接。

### 4. Redis TTL自动回收 (被动安全网)
- **机制**: 这是防止系统因未知错误（如引擎进程意外崩溃）导致内存泄漏的最后一道防线。每个任务在Redis中创建的Hash Key都会被设置一个较长的过期时间（TTL，如30分钟）。
- **处理**: 如果一个任务的Key因为超时被Redis自动删除，一个后台的监控服务（可以基于Redis的Keyspace Notifications实现）会捕获到这个事件。随后，该服务会去更新MySQL和MongoDB中对应任务的状态为 `'FAILED_ORPHANED'`，确保所有系统的数据最终保持一致，并记录下这次意外。

---

## 附录A: Redis任务Hash结构详解

`task:{task_id}` 是一个 Redis Hash，作为任务执行期间的"实时白板"，其包含的字段和示例如下：

| 字段名 (Field Name) | 类型 (Type) | 描述 |
| :--- | :--- | :--- |
| **`task_id`** | `String` | 任务的唯一标识符。 |
| **`user_id`** | `String` | 发起任务的用户ID。 |
| **`status`** | `String` | **核心状态字段**。枚举值包括: `'PROCESSING'`, `'WAITING_FOR_INPUT'`, `'SUCCESS'`, `'FAILED'`, `'CANCELED'`。 |
| **`created_at`** | `Unix Timestamp` | 任务创建时间的Unix时间戳 (秒)。 |
| **`updated_at`** | `Unix Timestamp` | Hash内容最后更新时间的Unix时间戳 (秒)。 |
| **`progress`** | `Integer` | 任务进度的百分比，如 `10`, `50`, `90`。 |
| **`current_step_message`**| `String` | 当前执行步骤的简短描述，用于UI实时展示，如 "正在为您规划景点..."。 |
| **`raw_user_query`** | `String` | 用户未经处理的原始输入。 |
| **`parsed_intent`** | `JSON String`| LLM解析后的结构化意图，包含目的地、时间、偏好等。 |
| **`user_profile_snapshot`**| `JSON String`| 任务开始时从MySQL获取的用户画像和记忆快照。 |
| **`llm_reasoning_trace`**| `JSON String`| (调试用) 记录LLM决策过程的思考链（Chain of Thought）。 |
| **`tool_calls_log`** | `JSON String`| 一个JSON数组，记录所有工具调用的历史，包括参数、重试次数和最终结果摘要。 |
| **`poi_candidates`** | `JSON String`| (决策前) 景点精选前的完整候选POI列表。 |
| **`selected_pois`** | `JSON String`| (决策后) 经过LLM智能精选后，最终确定的核心POI列表。 |
| **`interactive_choice_request`** | `JSON String`| 当 `status` 为 `'WAITING_FOR_INPUT'` 时，此字段存储需要用户确认的信息，如 `{"message": "为您推荐了故宫，是否确认？", "options": [...]}`。|
| **`user_choice_response`** | `JSON String`| 用户在交互环节做出的选择。 |
| **`final_result`** | `JSON String`| 当任务成功时，存储最终生成的完整、结构化的行程JSON。 |
| **`error_message`** | `String` | 当任务失败时，存储详细的错误信息，包括错误类型 (如 'TIMEOUT', 'TOOL_FAILURE') 和具体内容。 |
