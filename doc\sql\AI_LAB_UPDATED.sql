-- =================================================================
-- Schema: dh_ailab (AI Lab 与 VPA 形象中心) - 更新版本
-- 将 actions JSON 字段改为关系数据库设计
-- =================================================================

-- 如果 Schema 不存在，则创建
CREATE SCHEMA IF NOT EXISTS `dh_ailab` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 切换到目标 Schema
USE `dh_ailab`;

-- -----------------------------------------------------
-- 步骤 1: 按逆向依赖顺序，安全删除已存在的旧表
-- -----------------------------------------------------
DROP TABLE IF EXISTS `user_unlocked_skills`;
DROP TABLE IF EXISTS `user_vpa_role_skills`;
DROP TABLE IF EXISTS `user_vpa_roles`;
DROP TABLE IF EXISTS `vpa_personas`;
DROP TABLE IF EXISTS `vpa_role_skills`;
DROP TABLE IF EXISTS `ai_skills`;
DROP TABLE IF EXISTS `vpa_skills`;
DROP TABLE IF EXISTS `vpa_roles`;
DROP TABLE IF EXISTS `ai_skill_categories`;
DROP TABLE IF EXISTS `vpa_generation_styles`;
DROP TABLE IF EXISTS `status_definitions`;


-- -----------------------------------------------------
-- 步骤 2: 创建字典表 (无依赖，先行)
-- -----------------------------------------------------

-- 表 1: `ai_skill_categories` - AI技能类别字典表
CREATE TABLE IF NOT EXISTS `ai_skill_categories` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `category_key` VARCHAR(50) NOT NULL COMMENT '类别的唯一英文标识',
  `name` VARCHAR(50) NOT NULL COMMENT '类别名称，用于UI显示',
  `display_order` INT NOT NULL DEFAULT 0 COMMENT '显示顺序，值越小越靠前',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_category_key_unique` (`category_key`)
) ENGINE=InnoDB COMMENT='AI技能的分类字典表';

-- 表 2: `vpa_generation_styles` - VPA形象生成风格字典表
CREATE TABLE IF NOT EXISTS `vpa_generation_styles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `style_key` VARCHAR(50) NOT NULL COMMENT '风格的唯一英文标识',
  `name` VARCHAR(50) NOT NULL COMMENT '风格名称，用于UI显示',
  `description` TEXT NULL COMMENT '该风格的详细描述或默认的Prompt提示词',
  `is_available` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '该风格当前是否可用',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_style_key_unique` (`style_key`)
) ENGINE=InnoDB COMMENT='VPA形象生成风格字典表';

-- 表 3: `status_definitions` - 通用状态字典表
CREATE TABLE IF NOT EXISTS `status_definitions` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `status_key` VARCHAR(50) NOT NULL COMMENT '状态的英文键',
  `scope` VARCHAR(50) NOT NULL COMMENT '作用域 (例如: VPA_INSTANCE)',
  `display_name` VARCHAR(50) NOT NULL COMMENT '状态的中文显示名',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_key_scope_unique` (`status_key`, `scope`)
) ENGINE=InnoDB COMMENT='通用的状态定义字典表';

-- 表 4: `vpa_roles` - VPA角色字典表 (新增)
CREATE TABLE IF NOT EXISTS `vpa_roles` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `role_key` VARCHAR(50) NOT NULL COMMENT '角色的唯一英文标识',
  `name` VARCHAR(50) NOT NULL COMMENT '角色名称，用于UI显示',
  `description` TEXT NULL COMMENT '角色的详细描述',
  `role_type` VARCHAR(30) NOT NULL COMMENT '角色类型 (例如: assistant, entertainer, teacher)',
  `base_asset_id` VARCHAR(100) NOT NULL COMMENT '角色的基础资产ID（外观、声音等）',
  `thumbnail_url` VARCHAR(255) NOT NULL COMMENT '角色的缩略图URL',
  `is_available` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '该角色当前是否可用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_role_key_unique` (`role_key`),
  INDEX `idx_role_type` (`role_type`)
) ENGINE=InnoDB COMMENT='VPA角色字典表';

-- 表 5: `vpa_skills` - VPA技能字典表 (新增)
CREATE TABLE IF NOT EXISTS `vpa_skills` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `skill_key` VARCHAR(50) NOT NULL COMMENT '技能的唯一英文标识',
  `name` VARCHAR(50) NOT NULL COMMENT '技能名称，用于UI显示',
  `description` TEXT NULL COMMENT '技能的详细描述',
  `skill_type` VARCHAR(30) NOT NULL COMMENT '技能类型 (例如: action, expression, interaction)',
  `external_asset_id` VARCHAR(100) NOT NULL COMMENT '关联到外部资产库的技能资产ID',
  `duration_ms` INT NULL COMMENT '技能执行持续时间（毫秒）',
  `trigger_conditions` JSON NULL COMMENT '技能触发条件（JSON格式）',
  `is_available` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '该技能当前是否可用',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_skill_key_unique` (`skill_key`),
  INDEX `idx_skill_type` (`skill_type`)
) ENGINE=InnoDB COMMENT='VPA技能字典表';

-- 表 6: `vpa_role_skills` - VPA角色与技能关联表 (新增)
CREATE TABLE IF NOT EXISTS `vpa_role_skills` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `role_id` INT NOT NULL COMMENT '逻辑外键, 关联到 vpa_roles.id',
  `skill_id` INT NOT NULL COMMENT '逻辑外键, 关联到 vpa_skills.id',
  `is_default` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为该角色的默认技能',
  `skill_level` TINYINT NOT NULL DEFAULT 1 COMMENT '技能等级 (1-5)',
  `display_order` INT NOT NULL DEFAULT 0 COMMENT '在该角色中的显示顺序',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_role_skill_unique` (`role_id`, `skill_id`),
  INDEX `idx_role_id` (`role_id`),
  INDEX `idx_skill_id` (`skill_id`)
) ENGINE=InnoDB COMMENT='VPA角色与技能的关联表';


-- -----------------------------------------------------
-- 步骤 3: 创建核心业务表
-- -----------------------------------------------------

-- 表 5: `ai_skills` - AI技能字典表
CREATE TABLE IF NOT EXISTS `ai_skills` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `skill_key` VARCHAR(50) NOT NULL COMMENT '技能的唯一英文标识 (程序中使用)',
  `category_id` INT NULL COMMENT '逻辑外键, 关联到 ai_skill_categories.id',
  `name` VARCHAR(50) NOT NULL COMMENT '技能名称 (用于列表等短文本场景)',
  `content` JSON NOT NULL COMMENT '技能的完整结构化内容，用于详情页展示',
  `unlock_cost_points` INT NOT NULL DEFAULT 0 COMMENT '解锁该技能所需消耗的积分',
  `is_public` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否对所有用户可见',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_skill_key_unique` (`skill_key`)
) ENGINE=InnoDB COMMENT='AI技能字典表';

-- 表 6: `user_unlocked_skills` - 用户已解锁技能关联表
CREATE TABLE IF NOT EXISTS `user_unlocked_skills` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL COMMENT '逻辑外键, 关联到 dh_user_profile.users.id',
  `skill_id` INT NOT NULL COMMENT '逻辑外键, 关联到 ai_skills.id',
  `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '用户是否开启此技能的主动服务',
  `unlocked_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '技能解锁时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_user_skill_unique` (`user_id`, `skill_id`)
) ENGINE=InnoDB COMMENT='用户已解锁的技能关联表';

-- 表 7: `user_vpa_roles` - 用户VPA角色关联表 (简化设计，直接关联用户和角色)
CREATE TABLE IF NOT EXISTS `user_vpa_roles` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_id` INT NOT NULL COMMENT '外键, 关联到 dh_user_profile.users.id (用户档案系统)',
  `role_id` INT NOT NULL COMMENT '逻辑外键, 关联到 vpa_roles.id (用户选择的VPA角色)',
  `nickname` VARCHAR(50) NOT NULL COMMENT '用户为该VPA角色设置的昵称',
  `is_currently_active` BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否为用户当前正在使用的VPA角色',
  
  -- 个性化配置
  `custom_asset_id` VARCHAR(100) NULL COMMENT '用户自定义的资产ID，如果为空则使用角色的base_asset_id',
  `custom_wake_word` VARCHAR(20) NULL COMMENT '自定义唤醒词',
  `custom_response` VARCHAR(50) NULL COMMENT '自定义应答语',
  
  -- 个性化记忆关联
  `memory_context` JSON NULL COMMENT '与该VPA角色相关的记忆上下文配置',
  `personality_traits` JSON NULL COMMENT '基于用户记忆定制的个性化特征',
  
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_user_role_unique` (`user_id`, `role_id`),
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_role_id` (`role_id`),
  INDEX `idx_user_active` (`user_id`, `is_currently_active`),
  CONSTRAINT `fk_user_vpa_role_user` FOREIGN KEY (`user_id`) REFERENCES `dh_user_profile`.`users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB COMMENT='用户VPA角色关联表，直接关联用户和VPA角色';

-- 表 8: `user_vpa_role_skills` - 用户VPA角色技能自定义表 (新增)
CREATE TABLE IF NOT EXISTS `user_vpa_role_skills` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `user_vpa_role_id` BIGINT NOT NULL COMMENT '逻辑外键, 关联到 user_vpa_roles.id',
  `skill_id` INT NOT NULL COMMENT '逻辑外键, 关联到 vpa_skills.id',
  `is_enabled` BOOLEAN NOT NULL DEFAULT TRUE COMMENT '用户是否启用该技能',
  `skill_level` TINYINT NOT NULL DEFAULT 1 COMMENT '用户设置的技能等级 (1-5)',
  `custom_trigger` VARCHAR(100) NULL COMMENT '用户自定义的触发条件',
  `added_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '技能添加时间',
  PRIMARY KEY (`id`),
  UNIQUE INDEX `idx_user_role_skill_unique` (`user_vpa_role_id`, `skill_id`),
  INDEX `idx_user_vpa_role_id` (`user_vpa_role_id`),
  INDEX `idx_skill_id` (`skill_id`)
) ENGINE=InnoDB COMMENT='用户VPA角色技能自定义表';


-- -----------------------------------------------------
-- 步骤 4: 为字典表插入初始数据
-- -----------------------------------------------------

-- 技能分类
INSERT INTO `ai_skill_categories` (`id`, `category_key`, `name`, `display_order`) VALUES
(1, 'family', '亲子家庭', 10),
(2, 'travel', '随车出行', 20),
(3, 'entertainment', '影音娱乐', 30),
(4, 'productivity', '智能助手', 40);

-- VPA生成风格
INSERT INTO `vpa_generation_styles` (`id`, `style_key`, `name`, `description`, `is_available`) VALUES
(1, 'cartoon', '卡通', '生成色彩鲜艳的卡通风格形象。', 1),
(2, 'realistic', '写实', '生成接近真人质感的写实风格形象。', 1),
(3, 'cyberpunk', '赛博朋克', '生成充满未来科技感的赛博朋克风格形象。', 1),
(4, 'fantasy', '奇幻', '生成带有魔法与神话元素的奇幻风格形象。', 0);

-- VPA实例状态
INSERT INTO `status_definitions` (`id`, `status_key`, `scope`, `display_name`) VALUES
(1, 'ACTIVE', 'VPA_INSTANCE', '可用'),
(2, 'GENERATING', 'VPA_INSTANCE', '制作中'),
(3, 'FAILED', 'VPA_INSTANCE', '制作失败'),
(4, 'ARCHIVED', 'VPA_INSTANCE', '已归档');

-- VPA角色字典数据
INSERT INTO `vpa_roles` (`id`, `role_key`, `name`, `description`, `role_type`, `base_asset_id`, `thumbnail_url`, `is_available`) VALUES
(1, 'assistant', '智能助手', '专业的AI助手角色，擅长回答问题和提供帮助', 'assistant', 'role_assistant_001', '/images/roles/assistant.jpg', 1),
(2, 'entertainer', '娱乐达人', '活泼开朗的娱乐角色，擅长互动和娱乐', 'entertainer', 'role_entertainer_001', '/images/roles/entertainer.jpg', 1),
(3, 'teacher', '温柔导师', '耐心细致的教师角色，擅长教学和指导', 'teacher', 'role_teacher_001', '/images/roles/teacher.jpg', 1),
(4, 'companion', '贴心伙伴', '温暖陪伴的伙伴角色，擅长聊天和情感支持', 'companion', 'role_companion_001', '/images/roles/companion.jpg', 1);

-- VPA技能字典数据
INSERT INTO `vpa_skills` (`id`, `skill_key`, `name`, `description`, `skill_type`, `external_asset_id`, `duration_ms`, `trigger_conditions`, `is_available`) VALUES
(1, 'greeting_wave', '挥手问候', '友好的挥手打招呼技能', 'action', 'skill_wave_001', 2000, '{"triggers": ["hello", "hi", "你好"]}', 1),
(2, 'greeting_bow', '鞠躬问候', '礼貌的鞠躬问候技能', 'action', 'skill_bow_001', 1500, '{"triggers": ["formal_greeting", "尊敬"]}', 1),
(3, 'idle_breathing', '自然呼吸', '自然的呼吸待机技能', 'expression', 'skill_breath_001', NULL, '{"triggers": ["idle", "waiting"]}', 1),
(4, 'speaking_normal', '正常对话', '正常语速的对话技能', 'interaction', 'skill_speak_normal_001', NULL, '{"triggers": ["conversation", "question"]}', 1),
(5, 'speaking_excited', '兴奋对话', '兴奋状态的对话技能', 'interaction', 'skill_speak_excited_001', NULL, '{"triggers": ["excitement", "celebration"]}', 1),
(6, 'gesture_thumbs_up', '点赞手势', '竖起大拇指的赞许技能', 'action', 'skill_thumbs_up_001', 1000, '{"triggers": ["praise", "good", "棒"]}', 1),
(7, 'gesture_thinking', '思考动作', '手托下巴的思考技能', 'action', 'skill_thinking_001', 3000, '{"triggers": ["thinking", "考虑"]}', 1),
(8, 'emotion_happy', '开心表情', '开心愉悦的表情技能', 'expression', 'skill_happy_001', 2000, '{"triggers": ["happy", "joy", "开心"]}', 1),
(9, 'emotion_surprised', '惊讶表情', '惊讶的表情技能', 'expression', 'skill_surprised_001', 1500, '{"triggers": ["surprise", "wow", "惊讶"]}', 1),
(10, 'dance_simple', '简单舞蹈', '简单的舞蹈动作技能', 'action', 'skill_dance_001', 5000, '{"triggers": ["dance", "music", "跳舞"]}', 1),
(11, 'explain_concept', '概念解释', '清晰解释复杂概念的技能', 'interaction', 'skill_explain_001', NULL, '{"triggers": ["explain", "what_is", "解释"]}', 1),
(12, 'storytelling', '讲故事', '生动讲述故事的技能', 'interaction', 'skill_story_001', NULL, '{"triggers": ["story", "tell_me", "故事"]}', 1);

-- 为角色配置技能关联
-- 智能助手角色的技能
INSERT INTO `vpa_role_skills` (`role_id`, `skill_id`, `is_default`, `skill_level`, `display_order`) VALUES
(1, 1, 1, 3, 1),   -- 挥手问候 (默认)
(1, 3, 0, 2, 2),   -- 自然呼吸
(1, 4, 1, 4, 3),   -- 正常对话 (默认)
(1, 6, 0, 3, 4),   -- 点赞手势
(1, 7, 0, 4, 5),   -- 思考动作
(1, 8, 0, 3, 6),   -- 开心表情
(1, 11, 1, 5, 7);  -- 概念解释 (默认)

-- 娱乐达人角色的技能
INSERT INTO `vpa_role_skills` (`role_id`, `skill_id`, `is_default`, `skill_level`, `display_order`) VALUES
(2, 1, 1, 4, 1),   -- 挥手问候 (默认)
(2, 3, 0, 2, 2),   -- 自然呼吸
(2, 5, 1, 5, 3),   -- 兴奋对话 (默认)
(2, 6, 0, 4, 4),   -- 点赞手势
(2, 8, 1, 5, 5),   -- 开心表情 (默认)
(2, 9, 0, 4, 6),   -- 惊讶表情
(2, 10, 1, 4, 7),  -- 简单舞蹈 (默认)
(2, 12, 0, 3, 8);  -- 讲故事

-- 温柔导师角色的技能
INSERT INTO `vpa_role_skills` (`role_id`, `skill_id`, `is_default`, `skill_level`, `display_order`) VALUES
(3, 2, 1, 4, 1),   -- 鞠躬问候 (默认)
(3, 3, 0, 3, 2),   -- 自然呼吸
(3, 4, 1, 5, 3),   -- 正常对话 (默认)
(3, 7, 0, 4, 4),   -- 思考动作
(3, 8, 0, 4, 5),   -- 开心表情
(3, 11, 1, 5, 6),  -- 概念解释 (默认)
(3, 12, 1, 4, 7);  -- 讲故事 (默认)

-- 贴心伙伴角色的技能
INSERT INTO `vpa_role_skills` (`role_id`, `skill_id`, `is_default`, `skill_level`, `display_order`) VALUES
(4, 1, 1, 3, 1),   -- 挥手问候 (默认)
(4, 3, 0, 3, 2),   -- 自然呼吸
(4, 4, 1, 4, 3),   -- 正常对话 (默认)
(4, 6, 0, 3, 4),   -- 点赞手势
(4, 8, 1, 4, 5),   -- 开心表情 (默认)
(4, 9, 0, 3, 6),   -- 惊讶表情
(4, 12, 0, 4, 7);  -- 讲故事

 
-- -----------------------------------------------------
-- 步骤 5: 示例用户VPA角色关联数据
-- -----------------------------------------------------

-- 示例：为用户ID=1创建VPA角色关联（实际使用时，这些数据通过应用程序创建）
-- INSERT INTO `user_vpa_roles` (`user_id`, `role_id`, `nickname`, `is_currently_active`) VALUES
-- (1, 1, '我的小助手', 1),  -- 用户1选择智能助手角色作为当前活跃VPA
-- (1, 2, '活力伙伴', 0);   -- 用户1也拥有娱乐达人角色，但未激活

-- 注意：实际的用户VPA角色关联数据应该通过应用程序API创建，
-- 而不是在数据库初始化时预设，因为这些是用户的个人数据。