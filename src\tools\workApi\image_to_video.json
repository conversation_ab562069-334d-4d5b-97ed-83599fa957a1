{"4": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "加载CLIP视觉"}}, "5": {"inputs": {"strength_1": 1, "strength_2": 1, "crop": "center", "combine_embeds": "average", "force_offload": true, "tiles": 0, "ratio": 0.20000000000000004, "clip_vision": ["4", 0], "image_1": ["11", 0]}, "class_type": "WanVideoClipVisionEncode", "_meta": {"title": "WanVideo ClipVision Encode"}}, "6": {"inputs": {"weight": 2, "start_percent": 0, "end_percent": 1}, "class_type": "WanVideoEnhanceAVideo", "_meta": {"title": "WanVideo Enhance-A-Video"}}, "7": {"inputs": {"lora": "wan/Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank32.safetensors", "strength": 1.0000000000000002, "low_mem_load": false}, "class_type": "WanVideoLoraSelect", "_meta": {"title": "WanVideo Lora Select"}}, "8": {"inputs": {"blocks_to_swap": 30, "offload_img_emb": false, "offload_txt_emb": false, "use_non_blocking": true, "vace_blocks_to_swap": 0}, "class_type": "WanVideoBlockSwap", "_meta": {"title": "WanVideo BlockSwap"}}, "9": {"inputs": {"model_name": "umt5-xxl-enc-bf16.safetensors", "precision": "bf16", "load_device": "offload_device", "quantization": "fp8_e4m3fn"}, "class_type": "LoadWanVideoT5TextEncoder", "_meta": {"title": "Load WanVideo T5 TextEncoder"}}, "10": {"inputs": {"text": "将静态的旅游照片增加一点动态即可", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "Text Multiline"}}, "11": {"inputs": {"image": "微信图片_20250630091608.jpg"}, "class_type": "LoadImage", "_meta": {"title": "加载图像"}}, "12": {"inputs": {"model": "Wan2_1-I2V-14B-480P_fp8_e4m3fn.safetensors", "base_precision": "bf16", "quantization": "fp8_e4m3fn", "load_device": "offload_device", "attention_mode": "sageattn", "block_swap_args": ["8", 0], "lora": ["7", 0]}, "class_type": "WanVideoModelLoader", "_meta": {"title": "WanVideo Model Loader"}}, "13": {"inputs": {"offload_percent": 1}, "class_type": "WanVideoVRAMManagement", "_meta": {"title": "WanVideo VRAM Management"}}, "14": {"inputs": {"model_name": "wan_2.1_vae.safetensors", "precision": "bf16"}, "class_type": "WanVideoVAELoader", "_meta": {"title": "WanVideo VAE Loader"}}, "15": {"inputs": {"value": 768}, "class_type": "JWInteger", "_meta": {"title": "Integer"}}, "16": {"inputs": {"width": ["21", 3], "height": ["21", 4], "num_frames": ["23", 0], "noise_aug_strength": 0.030000000000000006, "start_latent_strength": 1, "end_latent_strength": 1, "force_offload": true, "fun_or_fl2v_model": false, "tiled_vae": false, "vae": ["14", 0], "clip_embeds": ["5", 0], "start_image": ["21", 0]}, "class_type": "WanVideoImageToVideoEncode", "_meta": {"title": "WanVideo ImageToVideo Encode"}}, "17": {"inputs": {"enable_vae_tiling": false, "tile_x": 272, "tile_y": 272, "tile_stride_x": 144, "tile_stride_y": 128, "normalization": "default", "vae": ["14", 0], "samples": ["19", 0]}, "class_type": "WanVideoDecode", "_meta": {"title": "WanVideo Decode"}}, "18": {"inputs": {"positive_prompt": ["10", 0], "negative_prompt": "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down", "force_offload": false, "speak_and_recognation": {"__value__": [false, true]}, "t5": ["9", 0]}, "class_type": "WanVideoTextEncode", "_meta": {"title": "WanVideo TextEncode"}}, "19": {"inputs": {"steps": 4, "cfg": 1.0000000000000002, "shift": 8.000000000000002, "seed": 1023435312925528, "force_offload": false, "scheduler": "lcm", "riflex_freq_index": 0, "denoise_strength": 1, "batched_cfg": "", "rope_function": "comfy", "model": ["12", 0], "image_embeds": ["16", 0], "text_embeds": ["18", 0], "feta_args": ["6", 0]}, "class_type": "WanVideoSampler", "_meta": {"title": "WanVide<PERSON>"}}, "20": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "WanVideoWrapper_I2V", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "images": ["17", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "21": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "16", "scale_to_side": "longest", "scale_to_length": ["15", 0], "background_color": "#000000", "image": ["11", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "23": {"inputs": {"value": 41}, "class_type": "JWInteger", "_meta": {"title": "Integer"}}}