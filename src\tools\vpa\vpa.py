import asyncio
import uvicorn
import json
from fastapi import FastAPI, HTTPException, Query, Body
from pydantic import BaseModel, Field, ValidationError
from typing import List, Optional, Any
import httpx
import time
import uuid
import logging
from tools.Tele.sign_utils import getAuth
from src.core.config import get_settings

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

API_IP =  "***************"
TEXT_TO_IMAGE_PORT = 8000
IMAGE_TO_IMAGE_PORT = 8001
IMAGE_TO_GIF_PORT = 8002
CALLBACK_URL = "http://***********:8898/api/ai_lab/callback"

# 2. 创建 FastAPI 应用
app = FastAPI(
    title="独立参数 GET 回调接收API",
    description="一个通过独立的GET查询参数接收回调结果的API服务"
)

# 3. 封装为内部方法，不对外开放
async def _handle_individual_param_callback(
    payload: dict
):
    """
    通过独立的查询参数接收回调数据。
    - 顶层字段 (seq_id, code 等) 直接作为查询参数。
    - 嵌套的 'data' 对象需要作为 URL 编码后的 JSON 字符串传递。
    """
    seq_id = payload.get("seq_id")
    send_pts_ms = payload.get("send_pts_ms")
    code = payload.get("code")
    message = payload.get("message")
    flag = payload.get("flag")
    task_id = payload.get("task_id")
    data = payload.get("data")
    logger.info("========= 成功接收到新的 GET 回调结果 (独立参数) =========")
    logger.info(f"请求序列号 (seq_id): {seq_id}")
    logger.info(f"时间戳 (send_pts_ms): {send_pts_ms}")
    logger.info(f"状态码 (code): {code}")
    logger.info(f"描述信息 (message): {message}")
    logger.info(f"业务标志 (flag): {'成功' if flag == 1 else '失败'}")
    logger.info(f"任务ID (task_id): {task_id}")
    logger.info(f"返回详情 (data): {data}")
    logger.info("========================================================\n")
    return {"status": "success", "message": "回调已成功接收 (独立参数)"}

async def _generate_text_to_img(
    prompt: str
):
    seq_id = f"seq_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
    post_payload = {
        "seq_id": seq_id,
        "task_id": 10011,
        "prompt": prompt,
        "resolution": [1024, 1024],
        "callback_url": f"{CALLBACK_URL}"
    }
    logger.info(f"准备调用目标接口: {API_IP}")
    logger.info(f"发送的 Payload: {post_payload}")
    max_retries = 3
    timeout_sec = 30.0
    for attempt in range(1, max_retries + 1):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"http://{API_IP}:{TEXT_TO_IMAGE_PORT}/openapi/Multimodal",
                    json=post_payload,
                    timeout=timeout_sec
                )
                response.raise_for_status()
            response_data = response.json()
            logger.info(f"从目标接口接收到的响应: {response_data}")
            return response_data
        except (httpx.RequestError, httpx.HTTPStatusError) as exc:
            logger.warning(f"[text_to_img] 第{attempt}次调用失败: {exc}")
            if attempt == max_retries:
                raise HTTPException(
                    status_code=503,
                    detail=f"无法连接到下游服务: {exc}"
                )
            await asyncio.sleep(1.5 * attempt)

async def _generate_img_to_img(
    prompt: str,
    image: str
):
    seq_id = f"seq_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
    post_payload = {
        "seq_id": seq_id,
        "task_id": 20031,
        "prompt": prompt,
        "image":[image],
        "image_type": 0,
        "callback_url": f"{CALLBACK_URL}",
        "extra_message": [
          {
            "key":"controlnet_type",
            "value":"canny"
          },
          {
            "key":"strength",
            "value":0.6
        #   },
        #   {
        #     "key":"scale",
        #     "value":3.0
          }
        ]
    }
    logger.info(f"准备调用目标接口: {API_IP}")
    logger.info(f"发送的 Payload: {post_payload}")
    max_retries = 3
    timeout_sec = 30.0
    for attempt in range(1, max_retries + 1):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"http://{API_IP}:{IMAGE_TO_IMAGE_PORT}/openapi/Multimodal",
                    json=post_payload,
                    timeout=timeout_sec
                )
                response.raise_for_status()
            response_data = response.json()
            logger.info(f"从目标接口接收到的响应: {response_data}")
            return response_data
        except (httpx.RequestError, httpx.HTTPStatusError) as exc:
            logger.warning(f"[img_to_img] 第{attempt}次调用失败: {exc}")
            if attempt == max_retries:
                raise HTTPException(
                    status_code=503,
                    detail=f"无法连接到下游服务: {exc}"
                )
            await asyncio.sleep(1.5 * attempt)

async def _generate_img_to_gif(
    prompt: str,
    image: str
):
    seq_id = f"seq_{int(time.time() * 1000)}_{uuid.uuid4().hex[:8]}"
    post_payload = {
        "seq_id": seq_id,
        "task_id": 30009,
        "prompt": prompt,
        "image":[image],
        "image_type": 0,
        "resolution": [480,480],
        "callback_url": f"{CALLBACK_URL}",
        "extra_message": [
          {
            "key":"duration",
            "value":5
          }
        ]
    }
    logger.info(f"准备调用目标接口: {API_IP}")
    logger.info(f"发送的 Payload: {post_payload}")
    max_retries = 3
    timeout_sec = 30.0
    for attempt in range(1, max_retries + 1):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"http://{API_IP}:{IMAGE_TO_GIF_PORT}/openapi/Multimodal",
                    json=post_payload,
                    timeout=timeout_sec
                )
                response.raise_for_status()
            response_data = response.json()
            logger.info(f"从目标接口接收到的响应: {response_data}")
            return response_data
        except (httpx.RequestError, httpx.HTTPStatusError) as exc:
            logger.warning(f"[img_to_gif] 第{attempt}次调用失败: {exc}")
            if attempt == max_retries:
                raise HTTPException(
                    status_code=503,
                    detail=f"无法连接到下游服务: {exc}"
                )
            await asyncio.sleep(1.5 * attempt)


REMOTE_MODEL_API_URL = getattr(get_settings().tele_sign, "prompt").https_url
async def _call_model_api(user_input: str):
    headers = getAuth.get_auth("prompt")
    payload = {
        "model": "telechat-35b",
        "messages": [{
            "role": "user",
            "content": f"{user_input}，正面，全身照，人形态，两只脚站立，两只手放置身体两侧，高度50cm，与人物反色差的纯色背景图。以上是文生图提示词，帮我优化一下，字数不多于100字, 只输出最终优化结果，不要说明，不要字数统计。"
        }],
        "max_completion_tokens": 150,
        "stream": False
    }
    logger.info(f"Payload: {payload}")
    max_retries = 3
    timeout_sec = 30.0
    for attempt in range(1, max_retries + 1):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    REMOTE_MODEL_API_URL,
                    headers=headers,
                    json=payload,
                    timeout=timeout_sec
                )
                response.raise_for_status()
            response_data = response.json()
            logger.info(f"模型API响应: {response_data}")
            # 解析content内容
            content = None
            try:
                choices = response_data.get("choices")
                if choices and isinstance(choices, list) and len(choices) > 0:
                    message = choices[0].get("message")
                    if message:
                        content = message.get("content")
            except Exception as e:
                logger.warning(f"解析content失败: {e}")
            return content
        except (httpx.RequestError, httpx.HTTPStatusError) as exc:
            logger.warning(f"[call_model_api] 第{attempt}次调用失败: {exc}")
            if attempt == max_retries:
                raise HTTPException(
                    status_code=503,
                    detail=f"无法连接到模型API: {exc}"
                )
            await asyncio.sleep(1.5 * attempt)